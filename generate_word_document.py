#!/usr/bin/env python3
"""
生成Word格式的软件著作权登记申请表
包含系统架构图和流程图
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
import os

def add_heading_with_style(doc, text, level=1):
    """添加带样式的标题"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_table_with_data(doc, data, headers=None):
    """添加表格"""
    rows = len(data) + (1 if headers else 0)
    cols = len(data[0]) if data else 2
    
    table = doc.add_table(rows=rows, cols=cols)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.LEFT
    
    # 添加表头
    if headers:
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头样式
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True
    
    # 添加数据
    start_row = 1 if headers else 0
    for i, row_data in enumerate(data):
        row = table.rows[start_row + i]
        for j, cell_data in enumerate(row_data):
            row.cells[j].text = str(cell_data)
    
    return table

def add_architecture_diagram(doc):
    """添加系统架构图"""
    add_heading_with_style(doc, "四、软件系统架构图", level=2)
    
    # 添加架构图说明
    p = doc.add_paragraph()
    p.add_run("系统类图结构：").bold = True
    
    # 添加架构图内容
    architecture_text = """
TranscriberSystem (主系统)
├── AudioProcessor (音频处理器)
│   ├── supportedFormats: String[]
│   ├── targetSampleRate: int
│   ├── targetChannels: int
│   ├── preprocess(filePath): String
│   ├── reduceNoise(audioFile): String
│   ├── splitAudio(duration): String[]
│   └── getAudioInfo(): Dict

├── VideoProcessor (视频处理器)
│   ├── videoFormats: String[]
│   ├── ffmpegAvailable: boolean
│   ├── extractAudio(videoPath): String
│   ├── getVideoInfo(): Dict
│   ├── extractAudioSegment(): String
│   └── checkFFmpeg(): boolean

├── OnlineDownloader (在线下载器)
│   ├── supportedPlatforms: String[]
│   ├── tempDir: String
│   ├── download(url): String
│   ├── getVideoInfo(): Dict
│   ├── detectPlatform(): String
│   └── cleanup(): void

├── RecognitionEngine (语音识别引擎)
│   ├── WhisperEngine (Whisper引擎)
│   │   ├── modelSize: String
│   │   ├── whisperModel: Model
│   │   ├── transcribe(audioPath): String
│   │   ├── isAvailable(): boolean
│   │   └── convertLanguageCode(): String
│   │
│   ├── GoogleEngine (Google引擎)
│   │   ├── recognizer: Recognizer
│   │   ├── credentialsPath: String
│   │   ├── transcribe(audioPath): String
│   │   └── isAvailable(): boolean
│   │
│   └── AzureEngine (Azure引擎)
│       ├── speechKey: String
│       ├── speechRegion: String
│       ├── config: SpeechConfig
│       ├── transcribe(audioPath): String
│       └── isAvailable(): boolean

├── RealtimeRecorder (实时录音器)
│   ├── sampleRate: int
│   ├── channels: int
│   ├── chunkDuration: int
│   ├── isRecording: boolean
│   ├── startRecording(): String
│   ├── stopRecording(): void
│   ├── testMicrophone(): boolean
│   └── getAudioDevices(): List

├── TextFormatter (文本格式化器)
│   ├── punctuationMap: Dict
│   ├── fillerWords: Set
│   ├── meetingKeywords: Set
│   ├── format(text, options): String
│   ├── addPunctuation(): String
│   ├── detectSpeakers(): String
│   ├── generateSummary(): String
│   └── exportToFormats(): Dict

└── UserInterface (用户界面)
    ├── CLIInterface (命令行界面)
    │   ├── console: Console
    │   ├── showMainMenu(): String
    │   ├── processAudioFile(): void
    │   ├── startRealtimeRecording(): void
    │   └── showSettings(): void
    │
    └── WebInterface (Web界面)
        ├── app: StreamlitApp
        ├── renderHeader(): void
        ├── renderSidebar(): Dict
        ├── renderFileUpload(): void
        └── displayResults(): void
"""
    
    # 添加代码块样式的段落
    p = doc.add_paragraph(architecture_text)
    p.style = 'No Spacing'
    for run in p.runs:
        run.font.name = 'Courier New'
        run.font.size = Pt(9)

def add_flowchart_diagram(doc):
    """添加流程图"""
    add_heading_with_style(doc, "五、软件处理流程图", level=2)
    
    # 添加流程图说明
    p = doc.add_paragraph()
    p.add_run("主要业务流程：").bold = True
    
    # 添加流程图内容
    flowchart_text = """
开始
  ↓
选择输入类型
  ├─ 音频文件 → 音频处理模块
  │              ├─ 格式检查
  │              ├─ 音频预处理
  │              └─ 降噪处理
  │                  ↓
  ├─ 视频文件 → 视频处理模块
  │              ├─ 格式检查
  │              ├─ 提取音频
  │              └─ 音频转换
  │                  ↓
  ├─ 在线链接 → 在线下载模块
  │              ├─ 平台识别
  │              ├─ 内容下载
  │              └─ 音频提取
  │                  ↓
  └─ 实时录音 → 实时录音模块
                 ├─ 设备检测
                 ├─ 实时录制
                 └─ 流式处理
                     ↓
                语音识别引擎
                 ├─ Whisper → 本地识别
                 ├─ Google  → 云端识别
                 └─ Azure   → 企业识别
                     ↓
                文本后处理
                 ├─ 标点符号添加
                 ├─ 说话人识别
                 ├─ 段落划分
                 └─ 格式优化
                     ↓
                输出模块
                 ├─ TXT格式
                 ├─ Markdown格式
                 └─ HTML格式
                     ↓
                  完成
"""
    
    # 添加代码块样式的段落
    p = doc.add_paragraph(flowchart_text)
    p.style = 'No Spacing'
    for run in p.runs:
        run.font.name = 'Courier New'
        run.font.size = Pt(9)

def create_copyright_document():
    """创建软件著作权登记申请表Word文档"""
    
    # 创建文档
    doc = Document()
    
    # 设置文档标题
    title = doc.add_heading('软件著作权登记信息表', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加填表说明
    add_heading_with_style(doc, "填表说明", level=1)
    
    doc.add_paragraph("1. 著作权人信息：")
    doc.add_paragraph("   • 名称：公司申请填写公司全称，个人申请填写个人名字")
    doc.add_paragraph("   • 地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址")
    doc.add_paragraph("   • 证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码")
    
    doc.add_paragraph("2. 软件信息：")
    doc.add_paragraph("   • 软件的全称应该以'软件、系统、平台'三词之一结尾，简称应当与全称相关，比全称短")
    doc.add_paragraph("   • 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后")
    
    # 一、著作权人基础信息
    add_heading_with_style(doc, "一、著作权人基础信息", level=2)
    
    author_data = [
        ["名称", "【请填写著作权人姓名/公司名称】"],
        ["地址", "【请填写实名认证时所填写的省市地址】"],
        ["成立时间", "【请填写成立时间】"],
        ["统一信用代码", "【公司填写统一信用代码，个人填写身份证号码】"]
    ]
    add_table_with_data(doc, author_data)
    
    # 二、软件基本信息
    add_heading_with_style(doc, "二、软件基本信息", level=2)
    
    software_data = [
        ["权利取得方式", "☑ 原始取得  ☐ 继受取得"],
        ["全称", "智能会议记录转文字稿软件"],
        ["简称", "会议转录软件"],
        ["版本号", "V1.0"],
        ["权利范围", "☑ 全部  ☐ 部分权利"],
        ["软件分类", "☑ 应用软件  ☐ 嵌入式软件  ☐ 中间件  ☐ 操作系统"],
        ["软件说明", "☑ 原创  ☐ 修改（升级版本）"]
    ]
    add_table_with_data(doc, software_data)
    
    # 三、开发信息
    add_heading_with_style(doc, "三、开发信息", level=2)
    
    dev_data = [
        ["开发方式", "☑ 独立开发  ☐ 合作开发  ☐ 委托开发  ☐ 下达任务开发"],
        ["开发完成时间", "2025年07月29日"],
        ["发表状态", "☑ 已发表  ☐ 未发表"],
        ["首次发表时间", "2025年07月29日"],
        ["首次发表地点", "【请填写具体省市】"]
    ]
    add_table_with_data(doc, dev_data)
    
    # 添加系统架构图
    add_architecture_diagram(doc)
    
    # 添加流程图
    add_flowchart_diagram(doc)
    
    # 六、技术环境信息
    add_heading_with_style(doc, "六、技术环境信息", level=2)
    
    tech_data = [
        ["开发的硬件环境", "Intel i5以上CPU，8GB内存，100GB硬盘"],
        ["运行的硬件环境", "Intel i3以上CPU，4GB内存，50GB硬盘"],
        ["开发该软件的操作系统", "Windows 10/11, macOS 10.15+, Ubuntu 18.04+"],
        ["软件开发环境/开发工具", "Python 3.8+, VS Code, Git"],
        ["该软件的运行平台/操作系统", "Windows, macOS, Linux"],
        ["软件运行支撑环境/支持软件", "Python运行环境, FFmpeg, 浏览器"],
        ["编程语言", "Python, HTML, CSS, JavaScript"],
        ["源程序量", "约8000行"]
    ]
    add_table_with_data(doc, tech_data)
    
    # 七、功能描述
    add_heading_with_style(doc, "七、功能描述", level=2)
    
    func_data = [
        ["开发目的", "为会议记录和音视频转录提供智能化解决方案"],
        ["面向领域/行业", "办公自动化、教育培训、媒体制作、企业服务"],
        ["软件的主要功能", "支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。"],
        ["软件的技术特点", "集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。"]
    ]
    add_table_with_data(doc, func_data)
    
    # 八、核心功能模块详述
    add_heading_with_style(doc, "八、核心功能模块详述", level=2)
    
    doc.add_paragraph("1. 音频处理模块", style='Heading 3')
    doc.add_paragraph("• 支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式")
    doc.add_paragraph("• 音频预处理：格式转换、采样率调整、声道转换")
    doc.add_paragraph("• 音频增强：音量标准化、动态范围压缩")
    doc.add_paragraph("• 降噪处理：基于谱减法的噪声抑制")
    
    doc.add_paragraph("2. 语音识别引擎", style='Heading 3')
    doc.add_paragraph("• OpenAI Whisper本地识别：支持多种模型大小，离线工作")
    doc.add_paragraph("• Google Speech-to-Text云端识别：高精度在线识别")
    doc.add_paragraph("• Azure认知服务：企业级可靠性")
    doc.add_paragraph("• 多引擎备选机制：主引擎失败时自动切换")
    
    doc.add_paragraph("3. 实时录音模块", style='Heading 3')
    doc.add_paragraph("• 实时音频录制：支持麦克风和系统音频")
    doc.add_paragraph("• 流式转录：边录制边转录，实时显示结果")
    doc.add_paragraph("• 音频设备管理：自动检测和选择输入设备")
    doc.add_paragraph("• 音频质量监控：实时监控信号强度")
    
    doc.add_paragraph("4. 文本后处理模块", style='Heading 3')
    doc.add_paragraph("• 智能标点符号添加：根据语义自动添加标点")
    doc.add_paragraph("• 说话人识别和标记：简单的说话人分离")
    doc.add_paragraph("• 文本格式化：段落划分、语句整理")
    doc.add_paragraph("• 会议摘要生成：提取关键信息和决策要点")
    
    # 添加注意事项
    doc.add_paragraph()
    p = doc.add_paragraph()
    p.add_run("注意事项：").bold = True
    doc.add_paragraph("1. 请根据实际情况填写著作权人信息中的空白部分")
    doc.add_paragraph("2. 开发完成时间和发表时间请根据实际情况调整")
    doc.add_paragraph("3. 首次发表地点请填写具体的省市信息")
    doc.add_paragraph("4. 如需修改任何技术信息，请确保与实际软件功能相符")
    
    # 保存文档
    output_file = "软件著作权登记申请表.docx"
    doc.save(output_file)
    print(f"✅ Word文档已生成: {output_file}")
    
    return output_file

def main():
    """主函数"""
    try:
        # 检查是否安装了python-docx
        import docx
        print("📄 开始生成Word文档...")
        
        # 生成文档
        output_file = create_copyright_document()
        
        print(f"🎉 文档生成完成！")
        print(f"📁 文件位置: {os.path.abspath(output_file)}")
        print("\n📋 文档包含内容：")
        print("   • 完整的软件著作权登记信息表")
        print("   • 系统架构类图")
        print("   • 软件处理流程图")
        print("   • 详细的功能模块说明")
        print("\n💡 使用提示：")
        print("   • 请填写著作权人信息中的空白部分")
        print("   • 可根据实际情况调整技术参数")
        print("   • 文档可直接用于软件著作权申请")
        
    except ImportError:
        print("❌ 缺少依赖包 python-docx")
        print("请运行: pip install python-docx")
    except Exception as e:
        print(f"❌ 生成文档时出现错误: {e}")

if __name__ == "__main__":
    main()
