"""
音频处理器测试
"""

import os
import tempfile
import unittest
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.audio.processor import AudioProcessor


class TestAudioProcessor(unittest.TestCase):
    """音频处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = AudioProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        try:
            shutil.rmtree(self.temp_dir)
        except Exception:
            pass
    
    def test_supported_formats(self):
        """测试支持的格式检查"""
        # 支持的格式
        self.assertTrue(self.processor.is_supported_format("test.mp3"))
        self.assertTrue(self.processor.is_supported_format("test.wav"))
        self.assertTrue(self.processor.is_supported_format("test.m4a"))
        self.assertTrue(self.processor.is_supported_format("test.flac"))
        
        # 不支持的格式
        self.assertFalse(self.processor.is_supported_format("test.txt"))
        self.assertFalse(self.processor.is_supported_format("test.doc"))
    
    def test_audio_info_structure(self):
        """测试音频信息结构"""
        # 创建一个简单的测试音频文件（实际项目中需要真实的音频文件）
        # 这里只测试方法的存在性
        self.assertTrue(hasattr(self.processor, 'get_audio_info'))
        self.assertTrue(callable(self.processor.get_audio_info))
    
    def test_preprocess_method_exists(self):
        """测试预处理方法存在"""
        self.assertTrue(hasattr(self.processor, 'preprocess'))
        self.assertTrue(callable(self.processor.preprocess))
    
    def test_noise_reduction_method_exists(self):
        """测试降噪方法存在"""
        self.assertTrue(hasattr(self.processor, 'reduce_noise'))
        self.assertTrue(callable(self.processor.reduce_noise))
    
    def test_split_audio_method_exists(self):
        """测试音频分割方法存在"""
        self.assertTrue(hasattr(self.processor, 'split_audio'))
        self.assertTrue(callable(self.processor.split_audio))


if __name__ == '__main__':
    unittest.main()
