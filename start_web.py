#!/usr/bin/env python3
"""
Web界面启动脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    try:
        import streamlit
        print("✅ Streamlit 已安装")
        return True
    except ImportError:
        print("❌ Streamlit 未安装")
        print("请运行: pip install streamlit")
        return False

def start_web_interface():
    """启动Web界面"""
    if not check_dependencies():
        return False
    
    # Web界面文件路径
    web_file = Path(__file__).parent / "src" / "ui" / "web.py"
    
    if not web_file.exists():
        print(f"❌ Web界面文件不存在: {web_file}")
        return False
    
    print("🚀 启动Web界面...")
    print("浏览器将自动打开，如果没有请手动访问显示的URL")
    print("按 Ctrl+C 停止服务")
    
    try:
        # 启动Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            str(web_file),
            "--server.headless", "false",
            "--server.port", "8501"
        ])
        return True
    except KeyboardInterrupt:
        print("\n👋 Web服务已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    start_web_interface()
