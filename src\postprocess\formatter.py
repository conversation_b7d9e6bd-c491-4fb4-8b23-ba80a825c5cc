"""
文字稿后处理模块
提供文本格式化、标点符号优化、说话人识别等功能
"""

import re
import jieba
from typing import List, Dict, Optional
from datetime import datetime


class TextFormatter:
    """文字稿格式化器"""
    
    def __init__(self):
        """初始化格式化器"""
        # 初始化jieba分词
        jieba.initialize()
        
        # 常用标点符号映射
        self.punctuation_map = {
            '，': '，',
            '。': '。',
            '？': '？',
            '！': '！',
            '：': '：',
            '；': '；',
            '"': '"',
            '"': '"',
            ''': ''',
            ''': '''
        }
        
        # 语气词和填充词
        self.filler_words = {
            '嗯', '啊', '呃', '额', '那个', '这个', '就是说', '然后呢',
            '对吧', '是吧', '你知道', '怎么说', '就是', '那么'
        }
        
        # 会议常用词汇
        self.meeting_keywords = {
            '会议', '讨论', '决定', '建议', '提案', '投票', '通过',
            '反对', '赞成', '议题', '发言', '总结', '下次会议'
        }
    
    def format(self, text: str, options: Optional[Dict] = None) -> str:
        """
        格式化文字稿
        
        Args:
            text: 原始文字稿
            options: 格式化选项
            
        Returns:
            格式化后的文字稿
        """
        if not text.strip():
            return text
        
        # 默认选项
        default_options = {
            'add_punctuation': True,
            'remove_fillers': False,
            'add_paragraphs': True,
            'add_timestamps': False,
            'speaker_detection': False,
            'add_summary': False
        }
        
        if options:
            default_options.update(options)
        
        formatted_text = text
        
        # 1. 基本清理
        formatted_text = self._basic_cleanup(formatted_text)
        
        # 2. 添加标点符号
        if default_options['add_punctuation']:
            formatted_text = self._add_punctuation(formatted_text)
        
        # 3. 移除填充词
        if default_options['remove_fillers']:
            formatted_text = self._remove_fillers(formatted_text)
        
        # 4. 分段处理
        if default_options['add_paragraphs']:
            formatted_text = self._add_paragraphs(formatted_text)
        
        # 5. 说话人识别
        if default_options['speaker_detection']:
            formatted_text = self._detect_speakers(formatted_text)
        
        # 6. 添加时间戳
        if default_options['add_timestamps']:
            formatted_text = self._add_timestamps(formatted_text)
        
        # 7. 生成摘要
        if default_options['add_summary']:
            summary = self._generate_summary(formatted_text)
            formatted_text = f"{summary}\n\n{formatted_text}"
        
        return formatted_text.strip()
    
    def _basic_cleanup(self, text: str) -> str:
        """基本文本清理"""
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text)
        
        # 移除重复的标点符号
        text = re.sub(r'([。！？])\1+', r'\1', text)
        text = re.sub(r'([，；：])\1+', r'\1', text)
        
        # 统一引号
        text = re.sub(r'[""]', '"', text)
        text = re.sub(r'['']', "'", text)
        
        return text.strip()
    
    def _add_punctuation(self, text: str) -> str:
        """智能添加标点符号"""
        # 分句处理
        sentences = re.split(r'[。！？\n]', text)
        formatted_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 根据语义添加标点
            if self._is_question(sentence):
                sentence += '？'
            elif self._is_exclamation(sentence):
                sentence += '！'
            elif not sentence.endswith(('。', '！', '？', '：', '；')):
                sentence += '。'
            
            # 添加逗号
            sentence = self._add_commas(sentence)
            formatted_sentences.append(sentence)
        
        return ''.join(formatted_sentences)
    
    def _is_question(self, sentence: str) -> bool:
        """判断是否为疑问句"""
        question_words = ['什么', '怎么', '为什么', '哪里', '谁', '何时', '如何', '是否', '能否', '可以吗', '好吗']
        return any(word in sentence for word in question_words)
    
    def _is_exclamation(self, sentence: str) -> bool:
        """判断是否为感叹句"""
        exclamation_words = ['太好了', '真的', '当然', '绝对', '一定', '必须', '非常', '特别']
        return any(word in sentence for word in exclamation_words)
    
    def _add_commas(self, sentence: str) -> str:
        """添加逗号"""
        # 简单的逗号添加规则
        words = jieba.lcut(sentence)
        
        if len(words) <= 3:
            return sentence
        
        # 在连词前添加逗号
        conjunctions = ['但是', '然而', '不过', '而且', '并且', '因为', '所以', '如果', '虽然']
        
        for i, word in enumerate(words):
            if word in conjunctions and i > 0:
                words[i] = '，' + word
        
        return ''.join(words)
    
    def _remove_fillers(self, text: str) -> str:
        """移除填充词和语气词"""
        for filler in self.filler_words:
            text = text.replace(filler, '')
        
        # 清理多余空格
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def _add_paragraphs(self, text: str) -> str:
        """智能分段"""
        sentences = re.split(r'[。！？]', text)
        paragraphs = []
        current_paragraph = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            current_paragraph.append(sentence)
            
            # 分段条件：句子数量或主题变化
            if len(current_paragraph) >= 3 or self._is_topic_change(sentence):
                paragraphs.append('。'.join(current_paragraph) + '。')
                current_paragraph = []
        
        # 添加剩余句子
        if current_paragraph:
            paragraphs.append('。'.join(current_paragraph) + '。')
        
        return '\n\n'.join(paragraphs)
    
    def _is_topic_change(self, sentence: str) -> bool:
        """判断是否为主题转换"""
        topic_indicators = ['接下来', '然后', '另外', '此外', '最后', '总之', '综上', '下面']
        return any(indicator in sentence for indicator in topic_indicators)
    
    def _detect_speakers(self, text: str) -> str:
        """简单的说话人识别"""
        paragraphs = text.split('\n\n')
        formatted_paragraphs = []
        
        speaker_count = 1
        
        for i, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                continue
            
            # 简单规则：每个段落可能是不同的说话人
            if i == 0:
                speaker_label = f"说话人{speaker_count}: "
            else:
                # 根据内容判断是否切换说话人
                if self._should_switch_speaker(paragraph):
                    speaker_count = speaker_count % 3 + 1  # 最多3个说话人循环
                
                speaker_label = f"说话人{speaker_count}: "
            
            formatted_paragraphs.append(speaker_label + paragraph)
        
        return '\n\n'.join(formatted_paragraphs)
    
    def _should_switch_speaker(self, paragraph: str) -> bool:
        """判断是否应该切换说话人"""
        # 简单规则：包含对话标识词
        dialogue_indicators = ['我觉得', '我认为', '我建议', '我同意', '我反对', '是的', '不是', '对的']
        return any(indicator in paragraph for indicator in dialogue_indicators)
    
    def _add_timestamps(self, text: str) -> str:
        """添加时间戳"""
        current_time = datetime.now()
        timestamp = current_time.strftime("%Y-%m-%d %H:%M:%S")
        
        header = f"会议记录 - {timestamp}\n" + "="*50 + "\n\n"
        
        # 为每个段落添加相对时间戳
        paragraphs = text.split('\n\n')
        timestamped_paragraphs = []
        
        for i, paragraph in enumerate(paragraphs):
            if paragraph.strip():
                time_offset = f"[{i*2:02d}:{(i*30)%60:02d}] "
                timestamped_paragraphs.append(time_offset + paragraph)
        
        return header + '\n\n'.join(timestamped_paragraphs)
    
    def _generate_summary(self, text: str) -> str:
        """生成会议摘要"""
        # 提取关键信息
        key_points = []
        
        # 查找决定和行动项
        sentences = re.split(r'[。！？]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 识别重要句子
            if any(keyword in sentence for keyword in ['决定', '通过', '建议', '行动', '任务', '负责']):
                key_points.append(f"• {sentence}")
        
        if key_points:
            summary = "会议要点摘要：\n" + '\n'.join(key_points[:5])  # 最多5个要点
        else:
            summary = "会议摘要：本次会议进行了充分的讨论和交流。"
        
        return summary + "\n" + "-"*50
    
    def export_to_formats(self, text: str, formats: List[str] = None) -> Dict[str, str]:
        """
        导出为不同格式
        
        Args:
            text: 格式化后的文本
            formats: 导出格式列表 ['txt', 'md', 'html']
            
        Returns:
            不同格式的文本字典
        """
        if formats is None:
            formats = ['txt']
        
        results = {}
        
        if 'txt' in formats:
            results['txt'] = text
        
        if 'md' in formats:
            results['md'] = self._to_markdown(text)
        
        if 'html' in formats:
            results['html'] = self._to_html(text)
        
        return results
    
    def _to_markdown(self, text: str) -> str:
        """转换为Markdown格式"""
        # 添加标题
        markdown = "# 会议记录\n\n"
        
        # 处理段落
        paragraphs = text.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                # 如果包含说话人标识，使用引用格式
                if '说话人' in paragraph:
                    markdown += f"> {paragraph}\n\n"
                else:
                    markdown += f"{paragraph}\n\n"
        
        return markdown
    
    def _to_html(self, text: str) -> str:
        """转换为HTML格式"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>会议记录</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .speaker { color: #0066cc; font-weight: bold; }
                .paragraph { margin-bottom: 15px; }
            </style>
        </head>
        <body>
            <h1>会议记录</h1>
        """
        
        paragraphs = text.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                # 处理说话人标识
                if '说话人' in paragraph:
                    parts = paragraph.split(': ', 1)
                    if len(parts) == 2:
                        html += f'<div class="paragraph"><span class="speaker">{parts[0]}:</span> {parts[1]}</div>\n'
                    else:
                        html += f'<div class="paragraph">{paragraph}</div>\n'
                else:
                    html += f'<div class="paragraph">{paragraph}</div>\n'
        
        html += """
        </body>
        </html>
        """
        
        return html
