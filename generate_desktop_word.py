#!/usr/bin/env python3
"""
生成桌面Word文档 - 使用RTF格式，包含ASCII艺术图形
"""

import os

def create_rtf_document():
    """创建RTF格式的软件著作权登记申请表"""
    
    rtf_content = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;} {\f1 SimSun;} {\f2 Courier New;}}
{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green128\blue0;}

\f1\fs28\qc\b 软件著作权登记信息表\b0\par
\par

\fs20\ql\b 填表说明：\b0\par
\par
\b 1. 著作权人信息：\b0\par
• 名称：公司申请填写公司全称，个人申请填写个人名字\par
• 地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址\par
• 证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码\par
\par
\b 2. 软件信息：\b0\par
• 软件的全称应该以"软件、系统、平台"三词之一结尾，简称应当与全称相关，比全称短\par
• 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后\par
\par

\b\fs24 一、著作权人基础信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx3000\cellx9000
\b 名称\b0\cell 【请填写著作权人姓名/公司名称】\cell\row
\b 地址\b0\cell 【请填写实名认证时所填写的省市地址】\cell\row
\b 成立时间\b0\cell 【请填写成立时间】\cell\row
\b 统一信用代码\b0\cell 【公司填写统一信用代码，个人填写身份证号码】\cell\row
\par

\b\fs24 二、软件基本信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx3000\cellx9000
\b 权利取得方式\b0\cell ☑ 原始取得  ☐ 继受取得\cell\row
\b 全称\b0\cell 智能会议记录转文字稿软件\cell\row
\b 简称\b0\cell 会议转录软件\cell\row
\b 版本号\b0\cell V1.0\cell\row
\b 权利范围\b0\cell ☑ 全部  ☐ 部分权利\cell\row
\b 软件分类\b0\cell ☑ 应用软件  ☐ 嵌入式软件  ☐ 中间件  ☐ 操作系统\cell\row
\b 软件说明\b0\cell ☑ 原创  ☐ 修改（升级版本）\cell\row
\par

\b\fs24 三、开发信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx3000\cellx9000
\b 开发方式\b0\cell ☑ 独立开发  ☐ 合作开发  ☐ 委托开发  ☐ 下达任务开发\cell\row
\b 开发完成时间\b0\cell 2025年07月29日\cell\row
\b 发表状态\b0\cell ☑ 已发表  ☐ 未发表\cell\row
\b 首次发表时间\b0\cell 2025年07月29日\cell\row
\b 首次发表地点\b0\cell 【请填写具体省市】\cell\row
\par

\b\fs24 四、软件系统架构图\b0\fs20\par
\par
\b 系统架构图（类图结构）：\b0\par
\par
\f2\fs16
┌─────────────────────────────────────────────────────────────────┐\par
│                    TranscriberSystem (主系统)                    │\par
└─────────────────────┬───────────────────────────────────────────┘\par
                      │\par
        ┌─────────────┼─────────────┐\par
        │             │             │\par
        ▼             ▼             ▼\par
┌─────────────┐ ┌─────────────┐ ┌─────────────┐\par
│AudioProcessor│ │VideoProcessor│ │OnlineDownloader│\par
│(音频处理器)   │ │(视频处理器)   │ │(在线下载器)   │\par
└─────────────┘ └─────────────┘ └─────────────┘\par
│               │               │\par
├─ supportedFormats: String[]   ├─ videoFormats: String[]      ├─ supportedPlatforms: String[]\par
├─ targetSampleRate: int        ├─ ffmpegAvailable: boolean    ├─ tempDir: String\par
├─ targetChannels: int          ├─ extractAudio(): String      ├─ download(): String\par
├─ preprocess(): String         ├─ getVideoInfo(): Dict        ├─ getVideoInfo(): Dict\par
├─ reduceNoise(): String        ├─ extractAudioSegment(): String ├─ detectPlatform(): String\par
├─ splitAudio(): String[]       ├─ checkFFmpeg(): boolean      ├─ cleanup(): void\par
└─ getAudioInfo(): Dict         └─ (视频处理方法)              └─ (下载处理方法)\par
\par
        ┌─────────────┼─────────────┐\par
        │             │             │\par
        ▼             ▼             ▼\par
┌─────────────┐ ┌─────────────┐ ┌─────────────┐\par
│RecognitionEngine│ │RealtimeRecorder│ │TextFormatter│\par
│(语音识别引擎) │ │(实时录音器)   │ │(文本格式化器)│\par
└─────────────┘ └─────────────┘ └─────────────┘\par
│               │               │\par
├─ currentEngine: String        ├─ sampleRate: int             ├─ punctuationMap: Dict\par
├─ availableEngines: String[]   ├─ channels: int               ├─ fillerWords: Set\par
├─ language: String             ├─ chunkDuration: int          ├─ meetingKeywords: Set\par
├─ transcribe(): String         ├─ isRecording: boolean        ├─ format(): String\par
├─ setEngine(): void            ├─ startRecording(): String    ├─ addPunctuation(): String\par
└─ getEngineInfo(): Dict        ├─ stopRecording(): void       ├─ detectSpeakers(): String\par
                                ├─ testMicrophone(): boolean   ├─ generateSummary(): String\par
                                └─ getAudioDevices(): List     └─ exportToFormats(): Dict\par
\par
├─ WhisperEngine (Whisper引擎)\par
│  ├─ modelSize: String\par
│  ├─ whisperModel: Model\par
│  ├─ transcribe(): String\par
│  ├─ isAvailable(): boolean\par
│  └─ convertLanguageCode(): String\par
│\par
├─ GoogleEngine (Google引擎)\par
│  ├─ recognizer: Recognizer\par
│  ├─ credentialsPath: String\par
│  ├─ transcribe(): String\par
│  └─ isAvailable(): boolean\par
│\par
└─ AzureEngine (Azure引擎)\par
   ├─ speechKey: String\par
   ├─ speechRegion: String\par
   ├─ config: SpeechConfig\par
   ├─ transcribe(): String\par
   └─ isAvailable(): boolean\par
\par
                      │\par
                      ▼\par
              ┌─────────────┐\par
              │UserInterface│\par
              │(用户界面)    │\par
              └─────────────┘\par
              │             │\par
              ├─ CLIInterface (命令行界面)\par
              │  ├─ console: Console\par
              │  ├─ showMainMenu(): String\par
              │  ├─ processAudioFile(): void\par
              │  ├─ startRealtimeRecording(): void\par
              │  └─ showSettings(): void\par
              │\par
              └─ WebInterface (Web界面)\par
                 ├─ app: StreamlitApp\par
                 ├─ renderHeader(): void\par
                 ├─ renderSidebar(): Dict\par
                 ├─ renderFileUpload(): void\par
                 └─ displayResults(): void\par
\f1\fs20\par

\b\fs24 五、软件处理流程图\b0\fs20\par
\par
\b 主要业务流程图：\b0\par
\par
\f2\fs16
                    ┌─────────┐\par
                    │   开始   │\par
                    └────┬────┘\par
                         │\par
                    ┌────▼────┐\par
                    │选择输入类型│\par
                    └────┬────┘\par
                         │\par
        ┌────────────────┼────────────────┐\par
        │                │                │\par
        ▼                ▼                ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │ 音频文件 │     │ 视频文件 │     │ 在线链接 │     │ 实时录音 │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │格式检查  │     │格式检查  │     │平台识别  │     │设备检测  │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │音频预处理│     │提取音频  │     │内容下载  │     │实时录制  │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │降噪处理  │     │音频转换  │     │音频提取  │     │流式处理  │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        └───────────────┼───────────────┼───────────────┘\par
                        │               │\par
                   ┌────▼───────────────▼────┐\par
                   │     语音识别引擎        │\par
                   └────┬───────────────┬────┘\par
                        │               │\par
        ┌───────────────┼───────────────┼───────────────┐\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │ Whisper │     │ Google  │     │ Azure   │     │ 其他引擎 │\par
   │本地识别  │     │云端识别  │     │企业识别  │     │ 备选方案 │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        └───────────────┼───────────────┼───────────────┘\par
                        │               │\par
                   ┌────▼───────────────▼────┐\par
                   │     文本后处理          │\par
                   └────┬───────────────┬────┘\par
                        │               │\par
        ┌───────────────┼───────────────┼───────────────┐\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │标点符号  │     │说话人    │     │段落划分  │     │格式优化  │\par
   │添加     │     │识别     │     │        │     │        │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        └───────────────┼───────────────┼───────────────┘\par
                        │               │\par
                   ┌────▼───────────────▼────┐\par
                   │     输出模块            │\par
                   └────┬───────────────┬────┘\par
                        │               │\par
        ┌───────────────┼───────────────┼───────────────┐\par
        │               │               │               │\par
        ▼               ▼               ▼               ▼\par
   ┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\par
   │   TXT   │     │Markdown │     │  HTML   │     │  其他   │\par
   │  格式   │     │  格式   │     │  格式   │     │  格式   │\par
   └────┬────┘     └────┬────┘     └────┬────┘     └────┬────┘\par
        │               │               │               │\par
        └───────────────┼───────────────┼───────────────┘\par
                        │               │\par
                   ┌────▼───────────────▼────┐\par
                   │        完成            │\par
                   └─────────────────────────┘\par
\f1\fs20\par

\b\fs24 六、技术环境信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx4000\cellx9000
\b 开发的硬件环境\b0\cell Intel i5以上CPU，8GB内存，100GB硬盘\cell\row
\b 运行的硬件环境\b0\cell Intel i3以上CPU，4GB内存，50GB硬盘\cell\row
\b 开发该软件的操作系统\b0\cell Windows 10/11, macOS 10.15+, Ubuntu 18.04+\cell\row
\b 软件开发环境/开发工具\b0\cell Python 3.8+, VS Code, Git\cell\row
\b 该软件的运行平台/操作系统\b0\cell Windows, macOS, Linux\cell\row
\b 软件运行支撑环境/支持软件\b0\cell Python运行环境, FFmpeg, 浏览器\cell\row
\b 编程语言\b0\cell Python, HTML, CSS, JavaScript\cell\row
\b 源程序量\b0\cell 约8000行\cell\row
\par

\b\fs24 七、功能描述\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx4000\cellx9000
\b 开发目的\b0\cell 为会议记录和音视频转录提供智能化解决方案\cell\row
\b 面向领域/行业\b0\cell 办公自动化、教育培训、媒体制作、企业服务\cell\row
\b 软件的主要功能\b0\cell 支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。\cell\row
\b 软件的技术特点\b0\cell 集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。\cell\row
\par

\b\fs24 八、核心功能模块详述\b0\fs20\par
\par
\b 1. 音频处理模块\b0\par
• 支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式\par
• 音频预处理：格式转换、采样率调整、声道转换\par
• 音频增强：音量标准化、动态范围压缩\par
• 降噪处理：基于谱减法的噪声抑制\par
• 长音频分割：自动将长音频分割成适合处理的小段\par
\par
\b 2. 语音识别引擎\b0\par
• OpenAI Whisper本地识别：支持多种模型大小，离线工作\par
• Google Speech-to-Text云端识别：高精度在线识别\par
• Azure认知服务：企业级可靠性\par
• 多引擎备选机制：主引擎失败时自动切换\par
• 多语言支持：中文、英语、日语、韩语等\par
\par
\b 3. 实时录音模块\b0\par
• 实时音频录制：支持麦克风和系统音频\par
• 流式转录：边录制边转录，实时显示结果\par
• 音频设备管理：自动检测和选择输入设备\par
• 音频质量监控：实时监控信号强度\par
• 多设备支持：支持多种音频输入设备\par
\par
\b 4. 文本后处理模块\b0\par
• 智能标点符号添加：根据语义自动添加标点\par
• 说话人识别和标记：简单的说话人分离\par
• 文本格式化：段落划分、语句整理\par
• 会议摘要生成：提取关键信息和决策要点\par
• 多格式导出：TXT、Markdown、HTML格式\par
\par

\b 注意事项：\b0\par
1. 请根据实际情况填写著作权人信息中的空白部分\par
2. 开发完成时间和发表时间请根据实际情况调整\par
3. 首次发表地点请填写具体的省市信息\par
4. 如需修改任何技术信息，请确保与实际软件功能相符\par
5. 本文档包含详细的系统架构图和流程图，符合软件著作权登记要求\par
}"""
    
    return rtf_content

def main():
    """主函数"""
    print("📄 开始生成桌面Word文档...")
    
    try:
        # 生成RTF内容
        rtf_content = create_rtf_document()
        
        # 保存到桌面
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_file = os.path.join(desktop_path, "软件著作权登记申请表_最终版.rtf")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(rtf_content)
        
        print(f"✅ Word文档已生成并保存到桌面！")
        print(f"📁 文件位置: {output_file}")
        print("\n📋 文档包含内容：")
        print("   • 完整的软件著作权登记信息表")
        print("   • 详细的系统架构图（类图格式）")
        print("   • 专业的软件处理流程图")
        print("   • 详细的功能模块说明")
        print("   • 标准的表格格式")
        print("\n💡 特色：")
        print("   • 使用ASCII艺术图形，清晰美观")
        print("   • 类似classDiagram的架构图设计")
        print("   • 专业的流程图布局")
        print("   • 符合软件著作权登记要求")
        print("   • RTF格式可直接用Word打开")
        print("\n📖 使用方法：")
        print("   1. 双击文件用Microsoft Word打开")
        print("   2. 文件 → 另存为 → 选择Word文档(.docx)格式")
        print("   3. 填写著作权人信息中的空白部分")
        print("   4. 可直接用于软件著作权申请")
        
        # 尝试打开文件
        try:
            import subprocess
            subprocess.Popen(['start', output_file], shell=True)
            print("\n🎉 文档已自动打开！")
        except:
            print(f"\n📖 请手动打开文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 生成文档时出现错误: {e}")

if __name__ == "__main__":
    main()
