"""
在线链接处理模块
支持从各种在线平台下载音频/视频内容
"""

import os
import tempfile
import re
from pathlib import Path
from typing import Optional, Dict, List
import yt_dlp
import requests
from urllib.parse import urlparse


class OnlineDownloader:
    """在线内容下载器"""
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化下载器
        
        Args:
            temp_dir: 临时文件目录，如果为None则使用系统临时目录
        """
        self.temp_dir = temp_dir or tempfile.mkdtemp()
        self.supported_platforms = {
            'youtube.com', 'youtu.be', 'bilibili.com', 'b23.tv',
            'tencent.com', 'qq.com', 'douyin.com', 'tiktok.com',
            'weibo.com', 'xiaohongshu.com'
        }
    
    def download(self, url: str, audio_only: bool = True, 
                output_path: Optional[str] = None) -> str:
        """
        下载在线内容
        
        Args:
            url: 在线链接
            audio_only: 是否只下载音频
            output_path: 输出文件路径
            
        Returns:
            下载的文件路径
        """
        if not self._is_valid_url(url):
            raise ValueError(f"无效的URL: {url}")
        
        platform = self._detect_platform(url)
        
        if platform in ['youtube', 'bilibili', 'tiktok', 'douyin']:
            return self._download_with_ytdlp(url, audio_only, output_path)
        elif platform == 'direct':
            return self._download_direct_file(url, output_path)
        else:
            # 尝试通用下载方法
            try:
                return self._download_with_ytdlp(url, audio_only, output_path)
            except Exception:
                return self._download_direct_file(url, output_path)
    
    def _is_valid_url(self, url: str) -> bool:
        """验证URL是否有效"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def _detect_platform(self, url: str) -> str:
        """检测平台类型"""
        domain = urlparse(url).netloc.lower()
        
        # 移除www前缀
        if domain.startswith('www.'):
            domain = domain[4:]
        
        if 'youtube.com' in domain or 'youtu.be' in domain:
            return 'youtube'
        elif 'bilibili.com' in domain or 'b23.tv' in domain:
            return 'bilibili'
        elif 'tiktok.com' in domain or 'douyin.com' in domain:
            return 'tiktok'
        elif 'tencent.com' in domain or 'qq.com' in domain:
            return 'tencent'
        elif self._is_direct_media_url(url):
            return 'direct'
        else:
            return 'unknown'
    
    def _is_direct_media_url(self, url: str) -> bool:
        """检查是否为直接媒体文件链接"""
        media_extensions = {
            '.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac',
            '.mp4', '.avi', '.mov', '.mkv', '.webm'
        }
        
        path = urlparse(url).path.lower()
        return any(path.endswith(ext) for ext in media_extensions)
    
    def _download_with_ytdlp(self, url: str, audio_only: bool = True,
                           output_path: Optional[str] = None) -> str:
        """使用yt-dlp下载"""
        if output_path is None:
            output_template = os.path.join(self.temp_dir, '%(title)s.%(ext)s')
        else:
            output_template = output_path
        
        # yt-dlp配置
        ydl_opts = {
            'outtmpl': output_template,
            'quiet': True,
            'no_warnings': True,
        }
        
        if audio_only:
            ydl_opts.update({
                'format': 'bestaudio/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'wav',
                    'preferredquality': '192',
                }],
                'postprocessor_args': [
                    '-ar', '16000',  # 采样率
                    '-ac', '1',      # 单声道
                ],
            })
        else:
            ydl_opts['format'] = 'best[height<=720]'  # 限制视频质量
        
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # 获取视频信息
                info = ydl.extract_info(url, download=False)
                title = info.get('title', 'downloaded_media')
                
                # 清理文件名
                safe_title = self._sanitize_filename(title)
                
                if output_path is None:
                    if audio_only:
                        output_path = os.path.join(self.temp_dir, f"{safe_title}.wav")
                    else:
                        ext = info.get('ext', 'mp4')
                        output_path = os.path.join(self.temp_dir, f"{safe_title}.{ext}")
                    
                    ydl_opts['outtmpl'] = output_path.replace('.wav', '.%(ext)s').replace('.mp4', '.%(ext)s')
                
                # 下载文件
                ydl.download([url])
                
                # 查找实际下载的文件
                if audio_only:
                    # 音频文件可能有不同的扩展名
                    base_name = os.path.splitext(output_path)[0]
                    for ext in ['.wav', '.mp3', '.m4a', '.webm']:
                        potential_path = base_name + ext
                        if os.path.exists(potential_path):
                            return potential_path
                
                return output_path
                
        except Exception as e:
            raise RuntimeError(f"yt-dlp下载失败: {str(e)}")
    
    def _download_direct_file(self, url: str, output_path: Optional[str] = None) -> str:
        """直接下载媒体文件"""
        if output_path is None:
            # 从URL获取文件名
            filename = os.path.basename(urlparse(url).path)
            if not filename or '.' not in filename:
                filename = 'downloaded_media.mp3'
            output_path = os.path.join(self.temp_dir, filename)
        
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return output_path
            
        except Exception as e:
            raise RuntimeError(f"直接下载失败: {str(e)}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', '_', filename)  # 替换空格
        filename = filename[:100]  # 限制长度
        
        return filename
    
    def get_video_info(self, url: str) -> Dict:
        """
        获取在线视频信息（不下载）
        
        Args:
            url: 视频链接
            
        Returns:
            视频信息字典
        """
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return {
                    'title': info.get('title', ''),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', ''),
                    'upload_date': info.get('upload_date', ''),
                    'view_count': info.get('view_count', 0),
                    'description': info.get('description', ''),
                    'thumbnail': info.get('thumbnail', ''),
                    'formats': len(info.get('formats', [])),
                    'has_audio': any(f.get('acodec') != 'none' for f in info.get('formats', [])),
                    'platform': self._detect_platform(url)
                }
                
        except Exception as e:
            raise RuntimeError(f"获取视频信息失败: {str(e)}")
    
    def get_supported_sites(self) -> List[str]:
        """获取支持的网站列表"""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                extractors = ydl.list_extractors()
                return [extractor.IE_NAME for extractor in extractors]
        except Exception:
            return list(self.supported_platforms)
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception:
            pass
