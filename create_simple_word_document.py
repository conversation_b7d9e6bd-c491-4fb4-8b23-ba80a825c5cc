#!/usr/bin/env python3
"""
生成简化版Word文档，使用ASCII艺术图形
"""

def create_word_document():
    """创建Word文档"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        
        # 创建文档
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading('软件著作权登记信息表', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加填表说明
        doc.add_heading("填表说明", level=1)
        doc.add_paragraph("1. 著作权人信息：")
        doc.add_paragraph("   • 名称：公司申请填写公司全称，个人申请填写个人名字")
        doc.add_paragraph("   • 地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址")
        doc.add_paragraph("   • 证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码")
        
        doc.add_paragraph("2. 软件信息：")
        doc.add_paragraph("   • 软件的全称应该以'软件、系统、平台'三词之一结尾，简称应当与全称相关，比全称短")
        doc.add_paragraph("   • 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后")
        
        # 一、著作权人基础信息
        doc.add_heading("一、著作权人基础信息", level=2)
        
        table1 = doc.add_table(rows=4, cols=2)
        table1.style = 'Table Grid'
        
        # 设置表头样式
        for cell in table1.rows[0].cells:
            cell.paragraphs[0].runs[0].font.bold = True if cell.paragraphs[0].runs else None
        
        table1.cell(0, 0).text = "名称"
        table1.cell(0, 1).text = "【请填写著作权人姓名/公司名称】"
        table1.cell(1, 0).text = "地址"
        table1.cell(1, 1).text = "【请填写实名认证时所填写的省市地址】"
        table1.cell(2, 0).text = "成立时间"
        table1.cell(2, 1).text = "【请填写成立时间】"
        table1.cell(3, 0).text = "统一信用代码"
        table1.cell(3, 1).text = "【公司填写统一信用代码，个人填写身份证号码】"
        
        # 二、软件基本信息
        doc.add_heading("二、软件基本信息", level=2)
        
        table2 = doc.add_table(rows=7, cols=2)
        table2.style = 'Table Grid'
        
        software_data = [
            ("权利取得方式", "☑ 原始取得  ☐ 继受取得"),
            ("全称", "智能会议记录转文字稿软件"),
            ("简称", "会议转录软件"),
            ("版本号", "V1.0"),
            ("权利范围", "☑ 全部  ☐ 部分权利"),
            ("软件分类", "☑ 应用软件  ☐ 嵌入式软件  ☐ 中间件  ☐ 操作系统"),
            ("软件说明", "☑ 原创  ☐ 修改（升级版本）")
        ]
        
        for i, (key, value) in enumerate(software_data):
            table2.cell(i, 0).text = key
            table2.cell(i, 1).text = value
        
        # 三、开发信息
        doc.add_heading("三、开发信息", level=2)
        
        table3 = doc.add_table(rows=5, cols=2)
        table3.style = 'Table Grid'
        
        dev_data = [
            ("开发方式", "☑ 独立开发  ☐ 合作开发  ☐ 委托开发  ☐ 下达任务开发"),
            ("开发完成时间", "2025年07月29日"),
            ("发表状态", "☑ 已发表  ☐ 未发表"),
            ("首次发表时间", "2025年07月29日"),
            ("首次发表地点", "【请填写具体省市】")
        ]
        
        for i, (key, value) in enumerate(dev_data):
            table3.cell(i, 0).text = key
            table3.cell(i, 1).text = value
        
        # 四、软件系统架构图
        doc.add_heading("四、软件系统架构图", level=2)
        
        # 添加ASCII艺术风格的架构图
        arch_diagram = """
┌─────────────────────────────────────────────────────────────────┐
│                    TranscriberSystem (主系统)                    │
└─────────────────────┬───────────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│AudioProcessor│ │VideoProcessor│ │OnlineDownloader│
│(音频处理器)   │ │(视频处理器)   │ │(在线下载器)   │
└─────────────┘ └─────────────┘ └─────────────┘
│               │               │
├─ preprocess() ├─ extractAudio()├─ download()
├─ reduceNoise()├─ getVideoInfo()├─ getVideoInfo()
├─ splitAudio() ├─ checkFFmpeg() ├─ detectPlatform()
└─ getAudioInfo()└─ extractSegment()└─ cleanup()

        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│RecognitionEngine│ │RealtimeRecorder│ │TextFormatter│
│(语音识别引擎) │ │(实时录音器)   │ │(文本格式化器)│
└─────────────┘ └─────────────┘ └─────────────┘
│               │               │
├─ WhisperEngine├─ startRecording()├─ format()
├─ GoogleEngine ├─ stopRecording() ├─ addPunctuation()
├─ AzureEngine  ├─ testMicrophone()├─ detectSpeakers()
└─ transcribe() └─ getAudioDevices()└─ generateSummary()

                      │
                      ▼
              ┌─────────────┐
              │UserInterface│
              │(用户界面)    │
              └─────────────┘
              │             │
              ├─ CLIInterface
              └─ WebInterface
"""
        
        # 添加架构图
        p = doc.add_paragraph()
        p.add_run("系统架构图：").bold = True
        
        arch_para = doc.add_paragraph(arch_diagram)
        arch_para.style = 'No Spacing'
        for run in arch_para.runs:
            run.font.name = 'Courier New'
            run.font.size = Pt(9)
        
        # 五、软件处理流程图
        doc.add_heading("五、软件处理流程图", level=2)
        
        # 添加流程图
        flow_diagram = """
                    ┌─────────┐
                    │   开始   │
                    └────┬────┘
                         │
                    ┌────▼────┐
                    │选择输入类型│
                    └────┬────┘
                         │
        ┌────────────────┼────────────────┐
        │                │                │
        ▼                ▼                ▼
   ┌─────────┐     ┌─────────┐     ┌─────────┐
   │ 音频文件 │     │ 视频文件 │     │ 在线链接 │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
        ▼               ▼               ▼
   ┌─────────┐     ┌─────────┐     ┌─────────┐
   │音频预处理│     │提取音频  │     │下载内容  │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
        └───────────────┼───────────────┘
                        │
                   ┌────▼────┐
                   │语音识别引擎│
                   └────┬────┘
                        │
        ┌───────────────┼───────────────┐
        │               │               │
        ▼               ▼               ▼
   ┌─────────┐     ┌─────────┐     ┌─────────┐
   │ Whisper │     │ Google  │     │ Azure   │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
        └───────────────┼───────────────┘
                        │
                   ┌────▼────┐
                   │文本后处理│
                   └────┬────┘
                        │
        ┌───────────────┼───────────────┐
        │               │               │
        ▼               ▼               ▼
   ┌─────────┐     ┌─────────┐     ┌─────────┐
   │标点添加  │     │说话人识别│     │段落划分  │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
        └───────────────┼───────────────┘
                        │
                   ┌────▼────┐
                   │ 输出模块 │
                   └────┬────┘
                        │
        ┌───────────────┼───────────────┐
        │               │               │
        ▼               ▼               ▼
   ┌─────────┐     ┌─────────┐     ┌─────────┐
   │   TXT   │     │Markdown │     │  HTML   │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
        └───────────────┼───────────────┘
                        │
                   ┌────▼────┐
                   │   完成   │
                   └─────────┘
"""
        
        p = doc.add_paragraph()
        p.add_run("软件处理流程图：").bold = True
        
        flow_para = doc.add_paragraph(flow_diagram)
        flow_para.style = 'No Spacing'
        for run in flow_para.runs:
            run.font.name = 'Courier New'
            run.font.size = Pt(9)
        
        # 六、技术环境信息
        doc.add_heading("六、技术环境信息", level=2)
        
        table4 = doc.add_table(rows=8, cols=2)
        table4.style = 'Table Grid'
        
        tech_data = [
            ("开发的硬件环境", "Intel i5以上CPU，8GB内存，100GB硬盘"),
            ("运行的硬件环境", "Intel i3以上CPU，4GB内存，50GB硬盘"),
            ("开发该软件的操作系统", "Windows 10/11, macOS 10.15+, Ubuntu 18.04+"),
            ("软件开发环境/开发工具", "Python 3.8+, VS Code, Git"),
            ("该软件的运行平台/操作系统", "Windows, macOS, Linux"),
            ("软件运行支撑环境/支持软件", "Python运行环境, FFmpeg, 浏览器"),
            ("编程语言", "Python, HTML, CSS, JavaScript"),
            ("源程序量", "约8000行")
        ]
        
        for i, (key, value) in enumerate(tech_data):
            table4.cell(i, 0).text = key
            table4.cell(i, 1).text = value
        
        # 七、功能描述
        doc.add_heading("七、功能描述", level=2)
        
        table5 = doc.add_table(rows=4, cols=2)
        table5.style = 'Table Grid'
        
        func_data = [
            ("开发目的", "为会议记录和音视频转录提供智能化解决方案"),
            ("面向领域/行业", "办公自动化、教育培训、媒体制作、企业服务"),
            ("软件的主要功能", "支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。"),
            ("软件的技术特点", "集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。")
        ]
        
        for i, (key, value) in enumerate(func_data):
            table5.cell(i, 0).text = key
            table5.cell(i, 1).text = value
        
        # 八、核心功能模块详述
        doc.add_heading("八、核心功能模块详述", level=2)
        
        modules = [
            ("1. 音频处理模块", [
                "支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式",
                "音频预处理：格式转换、采样率调整、声道转换",
                "音频增强：音量标准化、动态范围压缩",
                "降噪处理：基于谱减法的噪声抑制"
            ]),
            ("2. 语音识别引擎", [
                "OpenAI Whisper本地识别：支持多种模型大小，离线工作",
                "Google Speech-to-Text云端识别：高精度在线识别",
                "Azure认知服务：企业级可靠性",
                "多引擎备选机制：主引擎失败时自动切换"
            ]),
            ("3. 实时录音模块", [
                "实时音频录制：支持麦克风和系统音频",
                "流式转录：边录制边转录，实时显示结果",
                "音频设备管理：自动检测和选择输入设备",
                "音频质量监控：实时监控信号强度"
            ]),
            ("4. 文本后处理模块", [
                "智能标点符号添加：根据语义自动添加标点",
                "说话人识别和标记：简单的说话人分离",
                "文本格式化：段落划分、语句整理",
                "会议摘要生成：提取关键信息和决策要点"
            ])
        ]
        
        for module_name, features in modules:
            doc.add_paragraph(module_name, style='Heading 3')
            for feature in features:
                p = doc.add_paragraph()
                p.add_run("• ").bold = True
                p.add_run(feature)
        
        # 添加注意事项
        doc.add_paragraph()
        p = doc.add_paragraph()
        p.add_run("注意事项：").bold = True
        
        notes = [
            "请根据实际情况填写著作权人信息中的空白部分",
            "开发完成时间和发表时间请根据实际情况调整",
            "首次发表地点请填写具体的省市信息",
            "如需修改任何技术信息，请确保与实际软件功能相符"
        ]
        
        for i, note in enumerate(notes, 1):
            doc.add_paragraph(f"{i}. {note}")
        
        # 保存到桌面
        import os
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_file = os.path.join(desktop_path, "软件著作权登记申请表_最终版.docx")
        
        doc.save(output_file)
        
        return output_file
        
    except ImportError:
        print("❌ 缺少 python-docx 包")
        print("正在尝试安装...")
        import subprocess
        import sys
        
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'python-docx'])
            print("✅ python-docx 安装完成，请重新运行脚本")
            return None
        except:
            print("❌ 无法安装 python-docx")
            return None

def main():
    """主函数"""
    print("📄 开始生成Word文档...")
    
    try:
        output_file = create_word_document()
        
        if output_file:
            print(f"✅ Word文档已生成并保存到桌面！")
            print(f"📁 文件位置: {output_file}")
            print("\n📋 文档包含内容：")
            print("   • 完整的软件著作权登记信息表")
            print("   • ASCII艺术风格的系统架构图")
            print("   • 专业的软件处理流程图")
            print("   • 详细的功能模块说明")
            print("   • 标准的表格格式")
            print("\n💡 特色：")
            print("   • 使用ASCII艺术图形，清晰美观")
            print("   • 专业的流程图设计")
            print("   • 符合软件著作权登记要求")
            print("   • 可直接用于官方申请")
            
            # 尝试打开文件
            try:
                import subprocess
                subprocess.Popen(['start', output_file], shell=True)
                print("\n📖 文档已自动打开")
            except:
                print(f"\n📖 请手动打开文件: {output_file}")
        else:
            print("❌ 文档生成失败")
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")

if __name__ == "__main__":
    main()
