"""
命令行用户界面
提供交互式的命令行界面
"""

import os
import sys
from pathlib import Path
from typing import Optional
import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Prompt, Confirm
from rich.text import Text

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.audio.processor import AudioProcessor
from src.video.processor import VideoProcessor
from src.online.downloader import OnlineDownloader
from src.recognition.engine import RecognitionEngine
from src.realtime.recorder import RealtimeRecorder
from src.postprocess.formatter import TextFormatter

console = Console()


class TranscriberCLI:
    """转录器命令行界面"""
    
    def __init__(self):
        """初始化CLI"""
        self.audio_processor = AudioProcessor()
        self.video_processor = VideoProcessor()
        self.online_downloader = OnlineDownloader()
        self.text_formatter = TextFormatter()
    
    def show_welcome(self):
        """显示欢迎信息"""
        welcome_text = Text("🎙️ 会议记录转文字稿工具", style="bold blue")
        subtitle = Text("支持音频、视频、在线链接和实时录音", style="dim")
        
        panel = Panel.fit(
            f"{welcome_text}\n{subtitle}",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(panel)
    
    def show_main_menu(self) -> str:
        """显示主菜单"""
        console.print("\n📋 请选择操作模式：", style="bold")
        
        table = Table(show_header=False, box=None, padding=(0, 2))
        table.add_column("选项", style="cyan")
        table.add_column("描述", style="white")
        
        table.add_row("1", "处理音频文件")
        table.add_row("2", "处理视频文件")
        table.add_row("3", "处理在线链接")
        table.add_row("4", "实时录音转录")
        table.add_row("5", "查看设置")
        table.add_row("6", "退出程序")
        
        console.print(table)
        
        choice = Prompt.ask(
            "请输入选项",
            choices=["1", "2", "3", "4", "5", "6"],
            default="1"
        )
        
        return choice
    
    def process_audio_file(self):
        """处理音频文件"""
        console.print("\n🎵 音频文件处理", style="bold green")
        
        # 获取文件路径
        file_path = Prompt.ask("请输入音频文件路径")
        
        if not os.path.exists(file_path):
            console.print("❌ 文件不存在", style="bold red")
            return
        
        if not self.audio_processor.is_supported_format(file_path):
            console.print("❌ 不支持的音频格式", style="bold red")
            return
        
        # 显示文件信息
        try:
            info = self.audio_processor.get_audio_info(file_path)
            self._show_audio_info(info)
        except Exception as e:
            console.print(f"⚠️ 无法获取文件信息: {e}", style="yellow")
        
        # 选择处理选项
        self._process_file_common(file_path, 'audio')
    
    def process_video_file(self):
        """处理视频文件"""
        console.print("\n🎬 视频文件处理", style="bold green")
        
        # 检查ffmpeg
        if not self.video_processor.check_ffmpeg_available():
            console.print("⚠️ 未检测到FFmpeg，可能影响视频处理功能", style="yellow")
        
        # 获取文件路径
        file_path = Prompt.ask("请输入视频文件路径")
        
        if not os.path.exists(file_path):
            console.print("❌ 文件不存在", style="bold red")
            return
        
        if not self.video_processor.is_supported_format(file_path):
            console.print("❌ 不支持的视频格式", style="bold red")
            return
        
        # 显示文件信息
        try:
            info = self.video_processor.get_video_info(file_path)
            self._show_video_info(info)
        except Exception as e:
            console.print(f"⚠️ 无法获取文件信息: {e}", style="yellow")
        
        # 处理视频
        self._process_file_common(file_path, 'video')
    
    def process_online_link(self):
        """处理在线链接"""
        console.print("\n🔗 在线链接处理", style="bold green")
        
        # 获取链接
        url = Prompt.ask("请输入视频/音频链接")
        
        # 显示支持的平台
        console.print("支持的平台：YouTube, Bilibili, TikTok等", style="dim")
        
        # 获取视频信息
        try:
            with console.status("正在获取视频信息..."):
                info = self.online_downloader.get_video_info(url)
            
            self._show_online_info(info)
            
            if not Confirm.ask("是否继续下载和转录？"):
                return
            
        except Exception as e:
            console.print(f"⚠️ 无法获取视频信息: {e}", style="yellow")
            if not Confirm.ask("是否继续尝试下载？"):
                return
        
        # 处理在线内容
        self._process_file_common(url, 'online')
    
    def start_realtime_recording(self):
        """开始实时录音"""
        console.print("\n🎙️ 实时录音转录", style="bold green")
        
        # 选择语音识别引擎
        engine = self._select_recognition_engine()
        language = self._select_language()
        
        # 创建录音器
        recorder = RealtimeRecorder(engine=engine, language=language)
        
        # 测试麦克风
        console.print("正在测试麦克风...")
        if not recorder.test_microphone():
            console.print("❌ 麦克风测试失败", style="bold red")
            return
        
        # 显示音频设备
        devices = recorder.get_audio_devices()
        if devices:
            self._show_audio_devices(devices)
            
            if Confirm.ask("是否要选择特定的音频设备？"):
                device_id = int(Prompt.ask("请输入设备ID", default="0"))
                try:
                    recorder.set_input_device(device_id)
                except Exception as e:
                    console.print(f"⚠️ 设备设置失败: {e}", style="yellow")
        
        # 开始录音
        console.print("\n🔴 开始实时录音转录...", style="bold red")
        console.print("按 Ctrl+C 停止录音", style="dim")
        
        try:
            transcript = recorder.start_recording()
            
            if transcript.strip():
                # 保存结果
                output_path = self._get_output_path("realtime_transcript.txt")
                
                # 格式化选项
                if Confirm.ask("是否要格式化文本？"):
                    options = self._get_format_options()
                    transcript = self.text_formatter.format(transcript, options)
                
                # 保存文件
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(transcript)
                
                console.print(f"✅ 转录完成！文件已保存到: {output_path}", style="bold green")
                self._show_transcript_preview(transcript)
            else:
                console.print("⚠️ 未检测到有效的语音内容", style="yellow")
        
        except KeyboardInterrupt:
            console.print("\n⏹️ 录音已停止", style="yellow")
        except Exception as e:
            console.print(f"❌ 录音过程中出现错误: {e}", style="bold red")
    
    def show_settings(self):
        """显示设置"""
        console.print("\n⚙️ 系统设置", style="bold green")
        
        # 检查依赖
        self._check_dependencies()
        
        # 显示可用的识别引擎
        recognition_engine = RecognitionEngine()
        available_engines = recognition_engine.get_available_engines()
        
        table = Table(title="可用的语音识别引擎")
        table.add_column("引擎", style="cyan")
        table.add_column("状态", style="green")
        
        for engine in ['whisper', 'google', 'azure']:
            status = "✅ 可用" if engine in available_engines else "❌ 不可用"
            table.add_row(engine.title(), status)
        
        console.print(table)
    
    def _process_file_common(self, input_path: str, input_type: str):
        """通用文件处理流程"""
        # 选择语音识别引擎
        engine = self._select_recognition_engine()
        language = self._select_language()
        
        # 获取输出路径
        output_path = self._get_output_path()
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                console=console,
            ) as progress:
                
                # 预处理
                if input_type == 'audio':
                    task = progress.add_task("正在预处理音频...", total=100)
                    audio_file = self.audio_processor.preprocess(input_path)
                    progress.update(task, completed=100)
                
                elif input_type == 'video':
                    task = progress.add_task("正在提取音频...", total=100)
                    audio_file = self.video_processor.extract_audio(input_path)
                    progress.update(task, completed=100)
                
                elif input_type == 'online':
                    task = progress.add_task("正在下载内容...", total=100)
                    audio_file = self.online_downloader.download(input_path)
                    progress.update(task, completed=100)
                
                # 语音识别
                task = progress.add_task("正在进行语音识别...", total=100)
                recognition_engine = RecognitionEngine(engine=engine, language=language)
                transcript = recognition_engine.transcribe(audio_file)
                progress.update(task, completed=100)
            
            if not transcript.strip():
                console.print("⚠️ 未识别到有效的语音内容", style="yellow")
                return
            
            # 格式化选项
            if Confirm.ask("是否要格式化文本？"):
                options = self._get_format_options()
                transcript = self.text_formatter.format(transcript, options)
            
            # 保存文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(transcript)
            
            console.print(f"✅ 转录完成！文件已保存到: {output_path}", style="bold green")
            self._show_transcript_preview(transcript)
        
        except Exception as e:
            console.print(f"❌ 处理失败: {str(e)}", style="bold red")
    
    def _select_recognition_engine(self) -> str:
        """选择语音识别引擎"""
        recognition_engine = RecognitionEngine()
        available_engines = recognition_engine.get_available_engines()
        
        if not available_engines:
            console.print("❌ 没有可用的语音识别引擎", style="bold red")
            sys.exit(1)
        
        if len(available_engines) == 1:
            return available_engines[0]
        
        console.print("\n🤖 选择语音识别引擎：", style="bold")
        for i, engine in enumerate(available_engines, 1):
            console.print(f"{i}. {engine.title()}")
        
        choice = Prompt.ask(
            "请选择引擎",
            choices=[str(i) for i in range(1, len(available_engines) + 1)],
            default="1"
        )
        
        return available_engines[int(choice) - 1]
    
    def _select_language(self) -> str:
        """选择语言"""
        languages = {
            "1": ("zh-CN", "中文（简体）"),
            "2": ("en-US", "英语（美国）"),
            "3": ("ja-JP", "日语"),
            "4": ("ko-KR", "韩语")
        }
        
        console.print("\n🌐 选择语言：", style="bold")
        for key, (code, name) in languages.items():
            console.print(f"{key}. {name}")
        
        choice = Prompt.ask(
            "请选择语言",
            choices=list(languages.keys()),
            default="1"
        )
        
        return languages[choice][0]
    
    def _get_output_path(self, default_name: str = "transcript.txt") -> str:
        """获取输出路径"""
        output_path = Prompt.ask("请输入输出文件路径", default=default_name)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        return output_path
    
    def _get_format_options(self) -> dict:
        """获取格式化选项"""
        options = {}
        
        options['add_punctuation'] = Confirm.ask("添加标点符号？", default=True)
        options['remove_fillers'] = Confirm.ask("移除填充词？", default=False)
        options['add_paragraphs'] = Confirm.ask("智能分段？", default=True)
        options['speaker_detection'] = Confirm.ask("说话人识别？", default=False)
        options['add_timestamps'] = Confirm.ask("添加时间戳？", default=False)
        options['add_summary'] = Confirm.ask("生成摘要？", default=False)
        
        return options

    def _show_audio_info(self, info: dict):
        """显示音频文件信息"""
        table = Table(title="音频文件信息")
        table.add_column("属性", style="cyan")
        table.add_column("值", style="white")

        table.add_row("时长", f"{info['duration']:.2f} 秒")
        table.add_row("采样率", f"{info['sample_rate']} Hz")
        table.add_row("声道数", str(info['channels']))
        table.add_row("格式", info['format'])
        table.add_row("文件大小", f"{info['file_size'] / 1024 / 1024:.2f} MB")

        console.print(table)

    def _show_video_info(self, info: dict):
        """显示视频文件信息"""
        table = Table(title="视频文件信息")
        table.add_column("属性", style="cyan")
        table.add_column("值", style="white")

        table.add_row("时长", f"{info['duration']:.2f} 秒")
        table.add_row("分辨率", f"{info['width']}x{info['height']}")
        table.add_row("帧率", f"{info['fps']:.2f} fps")
        table.add_row("包含音频", "是" if info['has_audio'] else "否")
        if info['has_audio']:
            table.add_row("音频声道", str(info['audio_channels']))
            table.add_row("音频采样率", f"{info['audio_sample_rate']} Hz")
        table.add_row("文件大小", f"{info['file_size'] / 1024 / 1024:.2f} MB")

        console.print(table)

    def _show_online_info(self, info: dict):
        """显示在线视频信息"""
        table = Table(title="在线视频信息")
        table.add_column("属性", style="cyan")
        table.add_column("值", style="white")

        table.add_row("标题", info.get('title', '未知'))
        table.add_row("时长", f"{info.get('duration', 0):.0f} 秒")
        table.add_row("上传者", info.get('uploader', '未知'))
        table.add_row("观看次数", f"{info.get('view_count', 0):,}")
        table.add_row("平台", info.get('platform', '未知'))
        table.add_row("包含音频", "是" if info.get('has_audio') else "否")

        console.print(table)

    def _show_audio_devices(self, devices: list):
        """显示音频设备列表"""
        table = Table(title="可用音频设备")
        table.add_column("ID", style="cyan")
        table.add_column("设备名称", style="white")
        table.add_column("声道数", style="green")

        for device in devices:
            table.add_row(
                str(device['id']),
                device['name'],
                str(device['channels'])
            )

        console.print(table)

    def _show_transcript_preview(self, transcript: str):
        """显示转录预览"""
        lines = transcript.split('\n')
        preview_lines = lines[:10]  # 显示前10行

        preview_text = '\n'.join(preview_lines)
        if len(lines) > 10:
            preview_text += '\n...'

        panel = Panel(
            preview_text,
            title="转录预览",
            border_style="green",
            padding=(1, 2)
        )
        console.print(panel)

    def _check_dependencies(self):
        """检查依赖项"""
        console.print("正在检查依赖项...", style="dim")

        dependencies = {
            "FFmpeg": self.video_processor.check_ffmpeg_available(),
            "Whisper": True,  # 假设已安装
            "音频设备": len(RealtimeRecorder().get_audio_devices()) > 0
        }

        table = Table(title="依赖项检查")
        table.add_column("组件", style="cyan")
        table.add_column("状态", style="white")

        for name, available in dependencies.items():
            status = "✅ 可用" if available else "❌ 不可用"
            table.add_row(name, status)

        console.print(table)

    def run(self):
        """运行CLI主循环"""
        self.show_welcome()

        while True:
            try:
                choice = self.show_main_menu()

                if choice == "1":
                    self.process_audio_file()
                elif choice == "2":
                    self.process_video_file()
                elif choice == "3":
                    self.process_online_link()
                elif choice == "4":
                    self.start_realtime_recording()
                elif choice == "5":
                    self.show_settings()
                elif choice == "6":
                    console.print("👋 再见！", style="bold blue")
                    break

                # 询问是否继续
                if choice != "6":
                    console.print()
                    if not Confirm.ask("是否继续使用？", default=True):
                        console.print("👋 再见！", style="bold blue")
                        break

            except KeyboardInterrupt:
                console.print("\n👋 再见！", style="bold blue")
                break
            except Exception as e:
                console.print(f"❌ 程序错误: {str(e)}", style="bold red")
                if not Confirm.ask("是否继续？", default=True):
                    break


def main():
    """CLI主函数"""
    cli = TranscriberCLI()
    cli.run()


if __name__ == "__main__":
    main()
