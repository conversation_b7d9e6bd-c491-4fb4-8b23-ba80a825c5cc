"""
视频处理模块
支持从各种视频格式中提取音频
"""

import os
import tempfile
from pathlib import Path
from typing import Optional, Tuple
import ffmpeg
from moviepy.editor import VideoFileClip
import subprocess


class VideoProcessor:
    """视频处理器"""
    
    def __init__(self):
        """初始化视频处理器"""
        self.supported_formats = {
            '.mp4', '.avi', '.mov', '.mkv', '.flv', 
            '.wmv', '.webm', '.m4v', '.3gp', '.ogv'
        }
    
    def is_supported_format(self, file_path: str) -> bool:
        """检查视频格式是否支持"""
        return Path(file_path).suffix.lower() in self.supported_formats
    
    def extract_audio(self, video_path: str, output_path: Optional[str] = None,
                     audio_format: str = 'wav') -> str:
        """
        从视频文件中提取音频
        
        Args:
            video_path: 视频文件路径
            output_path: 输出音频文件路径，如果为None则使用临时文件
            audio_format: 输出音频格式 ('wav', 'mp3', 'flac')
            
        Returns:
            提取的音频文件路径
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        if not self.is_supported_format(video_path):
            raise ValueError(f"不支持的视频格式: {Path(video_path).suffix}")
        
        # 如果没有指定输出路径，使用临时文件
        if output_path is None:
            temp_dir = tempfile.mkdtemp()
            output_path = os.path.join(temp_dir, f"extracted_audio.{audio_format}")
        
        try:
            # 方法1: 使用ffmpeg-python (推荐)
            return self._extract_with_ffmpeg(video_path, output_path, audio_format)
        except Exception as e1:
            try:
                # 方法2: 使用moviepy作为备选
                return self._extract_with_moviepy(video_path, output_path, audio_format)
            except Exception as e2:
                raise RuntimeError(f"音频提取失败。FFmpeg错误: {str(e1)}, MoviePy错误: {str(e2)}")
    
    def _extract_with_ffmpeg(self, video_path: str, output_path: str, 
                           audio_format: str) -> str:
        """使用ffmpeg提取音频"""
        try:
            # 构建ffmpeg命令
            stream = ffmpeg.input(video_path)
            
            # 音频参数设置
            audio_params = {
                'acodec': self._get_audio_codec(audio_format),
                'ar': 16000,  # 采样率
                'ac': 1,      # 单声道
            }
            
            # 如果是wav格式，添加额外参数
            if audio_format.lower() == 'wav':
                audio_params['acodec'] = 'pcm_s16le'
            
            stream = ffmpeg.output(stream, output_path, **audio_params)
            
            # 执行转换，覆盖已存在的文件
            ffmpeg.run(stream, overwrite_output=True, quiet=True)
            
            return output_path
            
        except ffmpeg.Error as e:
            raise RuntimeError(f"FFmpeg处理失败: {e.stderr.decode() if e.stderr else str(e)}")
    
    def _extract_with_moviepy(self, video_path: str, output_path: str, 
                            audio_format: str) -> str:
        """使用moviepy提取音频"""
        try:
            # 加载视频文件
            video = VideoFileClip(video_path)
            
            # 提取音频
            audio = video.audio
            
            if audio is None:
                raise RuntimeError("视频文件中没有音频轨道")
            
            # 导出音频
            audio.write_audiofile(
                output_path,
                codec=self._get_moviepy_codec(audio_format),
                ffmpeg_params=['-ar', '16000', '-ac', '1'],  # 16kHz, 单声道
                verbose=False,
                logger=None
            )
            
            # 清理资源
            audio.close()
            video.close()
            
            return output_path
            
        except Exception as e:
            raise RuntimeError(f"MoviePy处理失败: {str(e)}")
    
    def _get_audio_codec(self, audio_format: str) -> str:
        """获取ffmpeg音频编码器"""
        codec_map = {
            'wav': 'pcm_s16le',
            'mp3': 'libmp3lame',
            'flac': 'flac',
            'aac': 'aac',
            'ogg': 'libvorbis'
        }
        return codec_map.get(audio_format.lower(), 'pcm_s16le')
    
    def _get_moviepy_codec(self, audio_format: str) -> str:
        """获取moviepy音频编码器"""
        codec_map = {
            'wav': 'pcm_s16le',
            'mp3': 'libmp3lame',
            'flac': 'flac',
            'aac': 'aac'
        }
        return codec_map.get(audio_format.lower(), 'pcm_s16le')
    
    def get_video_info(self, video_path: str) -> dict:
        """
        获取视频文件信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频信息字典
        """
        try:
            # 使用ffprobe获取视频信息
            probe = ffmpeg.probe(video_path)
            
            video_info = {
                'duration': 0,
                'width': 0,
                'height': 0,
                'fps': 0,
                'has_audio': False,
                'audio_channels': 0,
                'audio_sample_rate': 0,
                'file_size': os.path.getsize(video_path),
                'format': Path(video_path).suffix.lower()
            }
            
            # 解析流信息
            for stream in probe['streams']:
                if stream['codec_type'] == 'video':
                    video_info['duration'] = float(stream.get('duration', 0))
                    video_info['width'] = stream.get('width', 0)
                    video_info['height'] = stream.get('height', 0)
                    
                    # 计算帧率
                    fps_str = stream.get('r_frame_rate', '0/1')
                    if '/' in fps_str:
                        num, den = fps_str.split('/')
                        video_info['fps'] = float(num) / float(den) if float(den) != 0 else 0
                
                elif stream['codec_type'] == 'audio':
                    video_info['has_audio'] = True
                    video_info['audio_channels'] = stream.get('channels', 0)
                    video_info['audio_sample_rate'] = int(stream.get('sample_rate', 0))
                    
                    # 如果视频流没有duration，尝试从音频流获取
                    if video_info['duration'] == 0:
                        video_info['duration'] = float(stream.get('duration', 0))
            
            return video_info
            
        except Exception as e:
            raise RuntimeError(f"获取视频信息失败: {str(e)}")
    
    def extract_audio_segment(self, video_path: str, start_time: float, 
                            end_time: float, output_path: Optional[str] = None) -> str:
        """
        提取视频中指定时间段的音频
        
        Args:
            video_path: 视频文件路径
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            output_path: 输出音频文件路径
            
        Returns:
            提取的音频文件路径
        """
        if output_path is None:
            temp_dir = tempfile.mkdtemp()
            output_path = os.path.join(temp_dir, "audio_segment.wav")
        
        try:
            # 使用ffmpeg提取指定时间段
            stream = ffmpeg.input(video_path, ss=start_time, t=end_time - start_time)
            stream = ffmpeg.output(
                stream, output_path,
                acodec='pcm_s16le',
                ar=16000,
                ac=1
            )
            ffmpeg.run(stream, overwrite_output=True, quiet=True)
            
            return output_path
            
        except Exception as e:
            raise RuntimeError(f"音频段提取失败: {str(e)}")
    
    def check_ffmpeg_available(self) -> bool:
        """检查ffmpeg是否可用"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
