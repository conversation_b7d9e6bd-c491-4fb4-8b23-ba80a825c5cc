"""
文本格式化器测试
"""

import unittest
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.postprocess.formatter import TextFormatter


class TestTextFormatter(unittest.TestCase):
    """文本格式化器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.formatter = TextFormatter()
    
    def test_formatter_initialization(self):
        """测试格式化器初始化"""
        self.assertIsInstance(self.formatter, TextFormatter)
        self.assertIsInstance(self.formatter.punctuation_map, dict)
        self.assertIsInstance(self.formatter.filler_words, set)
        self.assertIsInstance(self.formatter.meeting_keywords, set)
    
    def test_basic_cleanup(self):
        """测试基本清理功能"""
        # 测试多余空格清理
        text = "这是  一个   测试"
        cleaned = self.formatter._basic_cleanup(text)
        self.assertEqual(cleaned, "这是 一个 测试")
        
        # 测试重复标点符号清理
        text = "这是测试。。。"
        cleaned = self.formatter._basic_cleanup(text)
        self.assertEqual(cleaned, "这是测试。")
    
    def test_is_question(self):
        """测试疑问句识别"""
        self.assertTrue(self.formatter._is_question("这是什么"))
        self.assertTrue(self.formatter._is_question("怎么办"))
        self.assertFalse(self.formatter._is_question("这是一个陈述句"))
    
    def test_is_exclamation(self):
        """测试感叹句识别"""
        self.assertTrue(self.formatter._is_exclamation("太好了"))
        self.assertTrue(self.formatter._is_exclamation("真的很棒"))
        self.assertFalse(self.formatter._is_exclamation("这是一个普通句子"))
    
    def test_remove_fillers(self):
        """测试填充词移除"""
        text = "嗯，这个，就是说，我觉得很好"
        cleaned = self.formatter._remove_fillers(text)
        # 应该移除填充词
        self.assertNotIn("嗯", cleaned)
        self.assertNotIn("这个", cleaned)
        self.assertNotIn("就是说", cleaned)
    
    def test_format_with_options(self):
        """测试带选项的格式化"""
        text = "这是一个测试文本"
        
        # 测试默认格式化
        formatted = self.formatter.format(text)
        self.assertIsInstance(formatted, str)
        self.assertGreater(len(formatted), 0)
        
        # 测试带选项的格式化
        options = {
            'add_punctuation': True,
            'remove_fillers': False,
            'add_paragraphs': True
        }
        formatted = self.formatter.format(text, options)
        self.assertIsInstance(formatted, str)
    
    def test_export_to_formats(self):
        """测试导出为不同格式"""
        text = "这是测试文本。\n\n这是第二段。"
        
        # 测试导出为TXT
        results = self.formatter.export_to_formats(text, ['txt'])
        self.assertIn('txt', results)
        self.assertEqual(results['txt'], text)
        
        # 测试导出为Markdown
        results = self.formatter.export_to_formats(text, ['md'])
        self.assertIn('md', results)
        self.assertIn('# 会议记录', results['md'])
        
        # 测试导出为HTML
        results = self.formatter.export_to_formats(text, ['html'])
        self.assertIn('html', results)
        self.assertIn('<html>', results['html'])
        self.assertIn('</html>', results['html'])
    
    def test_to_markdown(self):
        """测试Markdown转换"""
        text = "说话人1: 这是第一段。\n\n说话人2: 这是第二段。"
        markdown = self.formatter._to_markdown(text)
        
        self.assertIn('# 会议记录', markdown)
        self.assertIn('>', markdown)  # 引用格式
    
    def test_to_html(self):
        """测试HTML转换"""
        text = "说话人1: 这是第一段。\n\n这是普通段落。"
        html = self.formatter._to_html(text)
        
        self.assertIn('<!DOCTYPE html>', html)
        self.assertIn('<html>', html)
        self.assertIn('</html>', html)
        self.assertIn('<h1>会议记录</h1>', html)
        self.assertIn('class="speaker"', html)
    
    def test_generate_summary(self):
        """测试摘要生成"""
        text = "我们决定采用新的方案。大家一致通过了这个建议。下次会议将讨论具体实施。"
        summary = self.formatter._generate_summary(text)
        
        self.assertIsInstance(summary, str)
        self.assertIn('会议要点摘要', summary)
    
    def test_detect_speakers(self):
        """测试说话人识别"""
        text = "我觉得这个方案很好。\n\n我同意这个观点。\n\n让我们继续讨论。"
        result = self.formatter._detect_speakers(text)
        
        self.assertIn('说话人', result)
    
    def test_add_timestamps(self):
        """测试时间戳添加"""
        text = "这是第一段。\n\n这是第二段。"
        result = self.formatter._add_timestamps(text)
        
        self.assertIn('会议记录', result)
        self.assertIn('[', result)  # 时间戳格式
        self.assertIn(']', result)


if __name__ == '__main__':
    unittest.main()
