#!/usr/bin/env python3
"""
会议记录转文字稿程序 - 主入口
支持多种输入格式：音频文件、视频文件、在线链接、实时录音
"""

import click
import os
import sys
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.text import Text

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.audio.processor import AudioProcessor
from src.video.processor import VideoProcessor
from src.online.downloader import OnlineDownloader
from src.recognition.engine import RecognitionEngine
from src.realtime.recorder import RealtimeRecorder
from src.postprocess.formatter import TextFormatter

console = Console()

@click.command()
@click.option('--input', '-i', help='输入文件路径或在线链接')
@click.option('--output', '-o', help='输出文字稿文件路径', default='transcript.txt')
@click.option('--engine', '-e', 
              type=click.Choice(['whisper', 'google', 'azure', 'aws']),
              default='whisper',
              help='语音识别引擎选择')
@click.option('--language', '-l', default='zh-CN', help='语言代码 (如: zh-CN, en-US)')
@click.option('--realtime', '-r', is_flag=True, help='实时录音模式')
@click.option('--format-output', '-f', is_flag=True, help='格式化输出文本')
@click.option('--speaker-detection', '-s', is_flag=True, help='启用说话人识别')
def main(input, output, engine, language, realtime, format_output, speaker_detection):
    """
    会议记录转文字稿工具
    
    支持的输入格式：
    - 音频文件: MP3, WAV, M4A, FLAC, OGG等
    - 视频文件: MP4, AVI, MOV, MKV等
    - 在线链接: YouTube, 腾讯会议等
    - 实时录音: 麦克风录制
    """
    
    # 显示欢迎信息
    welcome_text = Text("🎙️ 会议记录转文字稿工具", style="bold blue")
    console.print(Panel(welcome_text, expand=False))
    
    try:
        if realtime:
            # 实时录音模式
            console.print("🔴 启动实时录音模式...", style="bold red")
            recorder = RealtimeRecorder(engine=engine, language=language)
            transcript = recorder.start_recording()
        else:
            if not input:
                console.print("❌ 请提供输入文件或使用 --realtime 进行实时录音", style="bold red")
                return
            
            # 处理输入文件或链接
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                
                # 判断输入类型
                if input.startswith(('http://', 'https://')):
                    # 在线链接
                    task = progress.add_task("正在下载在线内容...", total=None)
                    downloader = OnlineDownloader()
                    audio_file = downloader.download(input)
                    progress.update(task, description="下载完成 ✅")
                    
                elif input.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm')):
                    # 视频文件
                    task = progress.add_task("正在提取音频...", total=None)
                    video_processor = VideoProcessor()
                    audio_file = video_processor.extract_audio(input)
                    progress.update(task, description="音频提取完成 ✅")
                    
                else:
                    # 音频文件
                    task = progress.add_task("正在预处理音频...", total=None)
                    audio_processor = AudioProcessor()
                    audio_file = audio_processor.preprocess(input)
                    progress.update(task, description="音频预处理完成 ✅")
                
                # 语音识别
                task = progress.add_task("正在进行语音识别...", total=None)
                recognition_engine = RecognitionEngine(engine=engine, language=language)
                transcript = recognition_engine.transcribe(audio_file, speaker_detection=speaker_detection)
                progress.update(task, description="语音识别完成 ✅")
        
        # 后处理
        if format_output:
            console.print("📝 正在格式化文本...", style="yellow")
            formatter = TextFormatter()
            transcript = formatter.format(transcript)
        
        # 保存结果
        with open(output, 'w', encoding='utf-8') as f:
            f.write(transcript)
        
        console.print(f"✅ 转录完成！文字稿已保存到: {output}", style="bold green")
        
        # 显示预览
        preview_lines = transcript.split('\n')[:5]
        preview_text = '\n'.join(preview_lines)
        if len(transcript.split('\n')) > 5:
            preview_text += '\n...'
        
        console.print(Panel(preview_text, title="文字稿预览", border_style="green"))
        
    except Exception as e:
        console.print(f"❌ 处理过程中出现错误: {str(e)}", style="bold red")
        sys.exit(1)

if __name__ == "__main__":
    main()
