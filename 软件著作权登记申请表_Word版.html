
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" 
      xmlns:w="urn:schemas-microsoft-com:office:word" 
      xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta charset="UTF-8">
    <meta name="ProgId" content="Word.Document">
    <meta name="Generator" content="Microsoft Word">
    <meta name="Originator" content="Microsoft Word">
    <title>软件著作权登记信息表</title>
    <!--[if gte mso 9]>
    <xml>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
        @page {
            size: A4;
            margin: 2.5cm;
        }
        
        body {
            font-family: "Microsoft YaHei", "SimSun", serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #000;
        }
        
        .document-header {
            text-align: center;
            font-size: 20pt;
            font-weight: bold;
            margin-bottom: 30px;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
        }
        
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0 10px 0;
            background-color: #f0f0f0;
            padding: 8px;
            border-left: 4px solid #0066cc;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        table, th, td {
            border: 1px solid #000;
        }
        
        th, td {
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 25%;
        }
        
        .diagram {
            font-family: "Courier New", monospace;
            font-size: 10pt;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-line;
        }
        
        .flowchart {
            font-family: "Courier New", monospace;
            font-size: 10pt;
            background-color: #f0f8ff;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-line;
        }
        
        .note {
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }
        
        .fill-blank {
            border-bottom: 1px solid #000;
            min-width: 200px;
            display: inline-block;
            margin: 0 5px;
            padding: 2px 5px;
        }
    </style>
</head>
<body>
    <div class="document-header">
        软件著作权登记信息表
    </div>

    <div class="section-title">填表说明</div>
    <p><strong>1. 著作权人信息：</strong></p>
    <ul>
        <li>名称：公司申请填写公司全称，个人申请填写个人名字</li>
        <li>地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址</li>
        <li>证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码</li>
    </ul>
    <p><strong>2. 软件信息：</strong></p>
    <ul>
        <li>软件的全称应该以"软件、系统、平台"三词之一结尾，简称应当与全称相关，比全称短</li>
        <li>软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后</li>
    </ul>

    <div class="section-title">一、著作权人基础信息</div>
    <table>
        <tr>
            <th>名称</th>
            <td><span class="fill-blank" style="width: 400px;"></span></td>
        </tr>
        <tr>
            <th>地址</th>
            <td><span class="fill-blank" style="width: 400px;"></span>
                <div class="note">注：实名认证时所填写的省市</div>
            </td>
        </tr>
        <tr>
            <th>成立时间</th>
            <td><span class="fill-blank" style="width: 200px;"></span></td>
        </tr>
        <tr>
            <th>统一信用代码</th>
            <td><span class="fill-blank" style="width: 300px;"></span>
                <div class="note">注：著作权人为个人时，请填写身份证件号码</div>
            </td>
        </tr>
    </table>

    <div class="section-title">二、软件基本信息</div>
    <table>
        <tr>
            <th>权利取得方式</th>
            <td>☑ 原始取得 ☐ 继受取得</td>
        </tr>
        <tr>
            <th>全称</th>
            <td><strong>智能会议记录转文字稿软件</strong></td>
        </tr>
        <tr>
            <th>简称</th>
            <td><strong>会议转录软件</strong></td>
        </tr>
        <tr>
            <th>版本号</th>
            <td><strong>V1.0</strong></td>
        </tr>
        <tr>
            <th>权利范围</th>
            <td>☑ 全部 ☐ 部分权利</td>
        </tr>
        <tr>
            <th>软件分类</th>
            <td>☑ 应用软件 ☐ 嵌入式软件 ☐ 中间件 ☐ 操作系统</td>
        </tr>
        <tr>
            <th>软件说明</th>
            <td>☑ 原创 ☐ 修改（升级版本）</td>
        </tr>
    </table>

    <div class="section-title">三、开发信息</div>
    <table>
        <tr>
            <th>开发方式</th>
            <td>☑ 独立开发 ☐ 合作开发 ☐ 委托开发 ☐ 下达任务开发</td>
        </tr>
        <tr>
            <th>开发完成时间</th>
            <td><strong>2025年07月29日</strong>
                <div class="note">注：具体到年月日</div>
            </td>
        </tr>
        <tr>
            <th>发表状态</th>
            <td>☑ 已发表 ☐ 未发表</td>
        </tr>
        <tr>
            <th>首次发表时间</th>
            <td><strong>2025年07月29日</strong>
                <div class="note">注：具体到年月日</div>
            </td>
        </tr>
        <tr>
            <th>首次发表地点</th>
            <td><span class="fill-blank" style="width: 200px;"></span>
                <div class="note">注：具体到省市</div>
            </td>
        </tr>
    </table>

    <div class="section-title">四、软件系统架构图</div>
    <p><strong>系统类图结构：</strong></p>
    <div class="diagram">TranscriberSystem (主系统)
├── AudioProcessor (音频处理器)
│   ├── supportedFormats: String[]
│   ├── targetSampleRate: int
│   ├── targetChannels: int
│   ├── preprocess(filePath): String
│   ├── reduceNoise(audioFile): String
│   ├── splitAudio(duration): String[]
│   └── getAudioInfo(): Dict

├── VideoProcessor (视频处理器)
│   ├── videoFormats: String[]
│   ├── ffmpegAvailable: boolean
│   ├── extractAudio(videoPath): String
│   ├── getVideoInfo(): Dict
│   ├── extractAudioSegment(): String
│   └── checkFFmpeg(): boolean

├── OnlineDownloader (在线下载器)
│   ├── supportedPlatforms: String[]
│   ├── tempDir: String
│   ├── download(url): String
│   ├── getVideoInfo(): Dict
│   ├── detectPlatform(): String
│   └── cleanup(): void

├── RecognitionEngine (语音识别引擎)
│   ├── WhisperEngine (Whisper引擎)
│   │   ├── modelSize: String
│   │   ├── whisperModel: Model
│   │   ├── transcribe(audioPath): String
│   │   ├── isAvailable(): boolean
│   │   └── convertLanguageCode(): String
│   │
│   ├── GoogleEngine (Google引擎)
│   │   ├── recognizer: Recognizer
│   │   ├── credentialsPath: String
│   │   ├── transcribe(audioPath): String
│   │   └── isAvailable(): boolean
│   │
│   └── AzureEngine (Azure引擎)
│       ├── speechKey: String
│       ├── speechRegion: String
│       ├── config: SpeechConfig
│       ├── transcribe(audioPath): String
│       └── isAvailable(): boolean

├── RealtimeRecorder (实时录音器)
│   ├── sampleRate: int
│   ├── channels: int
│   ├── chunkDuration: int
│   ├── isRecording: boolean
│   ├── startRecording(): String
│   ├── stopRecording(): void
│   ├── testMicrophone(): boolean
│   └── getAudioDevices(): List

├── TextFormatter (文本格式化器)
│   ├── punctuationMap: Dict
│   ├── fillerWords: Set
│   ├── meetingKeywords: Set
│   ├── format(text, options): String
│   ├── addPunctuation(): String
│   ├── detectSpeakers(): String
│   ├── generateSummary(): String
│   └── exportToFormats(): Dict

└── UserInterface (用户界面)
    ├── CLIInterface (命令行界面)
    │   ├── console: Console
    │   ├── showMainMenu(): String
    │   ├── processAudioFile(): void
    │   ├── startRealtimeRecording(): void
    │   └── showSettings(): void
    │
    └── WebInterface (Web界面)
        ├── app: StreamlitApp
        ├── renderHeader(): void
        ├── renderSidebar(): Dict
        ├── renderFileUpload(): void
        └── displayResults(): void</div>

    <div class="section-title">五、软件处理流程图</div>
    <p><strong>主要业务流程：</strong></p>
    <div class="flowchart">开始
  ↓
选择输入类型
  ├─ 音频文件 → 音频处理模块
  │              ├─ 格式检查
  │              ├─ 音频预处理
  │              └─ 降噪处理
  │                  ↓
  ├─ 视频文件 → 视频处理模块
  │              ├─ 格式检查
  │              ├─ 提取音频
  │              └─ 音频转换
  │                  ↓
  ├─ 在线链接 → 在线下载模块
  │              ├─ 平台识别
  │              ├─ 内容下载
  │              └─ 音频提取
  │                  ↓
  └─ 实时录音 → 实时录音模块
                 ├─ 设备检测
                 ├─ 实时录制
                 └─ 流式处理
                     ↓
                语音识别引擎
                 ├─ Whisper → 本地识别
                 ├─ Google  → 云端识别
                 └─ Azure   → 企业识别
                     ↓
                文本后处理
                 ├─ 标点符号添加
                 ├─ 说话人识别
                 ├─ 段落划分
                 └─ 格式优化
                     ↓
                输出模块
                 ├─ TXT格式
                 ├─ Markdown格式
                 └─ HTML格式
                     ↓
                  完成</div>

    <div class="section-title">六、技术环境信息</div>
    <table>
        <tr>
            <th>开发的硬件环境</th>
            <td><strong>Intel i5以上CPU，8GB内存，100GB硬盘</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>运行的硬件环境</th>
            <td><strong>Intel i3以上CPU，4GB内存，50GB硬盘</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>开发该软件的操作系统</th>
            <td><strong>Windows 10/11, macOS 10.15+, Ubuntu 18.04+</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>软件开发环境/开发工具</th>
            <td><strong>Python 3.8+, VS Code, Git</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>该软件的运行平台/操作系统</th>
            <td><strong>Windows, macOS, Linux</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>软件运行支撑环境/支持软件</th>
            <td><strong>Python运行环境, FFmpeg, 浏览器</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>编程语言</th>
            <td><strong>Python, HTML, CSS, JavaScript</strong>
                <div class="note">注：要求120字以内</div>
            </td>
        </tr>
        <tr>
            <th>源程序量</th>
            <td><strong>约8000行</strong>
                <div class="note">注：按照实际代码量填写</div>
            </td>
        </tr>
    </table>

    <div class="section-title">七、功能描述</div>
    <table>
        <tr>
            <th>开发目的</th>
            <td><strong>为会议记录和音视频转录提供智能化解决方案</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>面向领域/行业</th>
            <td><strong>办公自动化、教育培训、媒体制作、企业服务</strong>
                <div class="note">注：要求50字以内</div>
            </td>
        </tr>
        <tr>
            <th>软件的主要功能</th>
            <td><strong>支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。</strong>
                <div class="note">注：要求200字以内</div>
            </td>
        </tr>
        <tr>
            <th>软件的技术特点</th>
            <td><strong>集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。</strong>
                <div class="note">注：要求100字以内</div>
            </td>
        </tr>
    </table>

    <div class="section-title">八、核心功能模块详述</div>
    
    <p><strong>1. 音频处理模块</strong></p>
    <ul>
        <li>支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式</li>
        <li>音频预处理：格式转换、采样率调整、声道转换</li>
        <li>音频增强：音量标准化、动态范围压缩</li>
        <li>降噪处理：基于谱减法的噪声抑制</li>
    </ul>

    <p><strong>2. 语音识别引擎</strong></p>
    <ul>
        <li>OpenAI Whisper本地识别：支持多种模型大小，离线工作</li>
        <li>Google Speech-to-Text云端识别：高精度在线识别</li>
        <li>Azure认知服务：企业级可靠性</li>
        <li>多引擎备选机制：主引擎失败时自动切换</li>
    </ul>

    <p><strong>3. 实时录音模块</strong></p>
    <ul>
        <li>实时音频录制：支持麦克风和系统音频</li>
        <li>流式转录：边录制边转录，实时显示结果</li>
        <li>音频设备管理：自动检测和选择输入设备</li>
        <li>音频质量监控：实时监控信号强度</li>
    </ul>

    <p><strong>4. 文本后处理模块</strong></p>
    <ul>
        <li>智能标点符号添加：根据语义自动添加标点</li>
        <li>说话人识别和标记：简单的说话人分离</li>
        <li>文本格式化：段落划分、语句整理</li>
        <li>会议摘要生成：提取关键信息和决策要点</li>
    </ul>

    <div style="margin-top: 40px; font-size: 10pt; color: #666; border-top: 1px solid #ddd; padding-top: 20px;">
        <p><strong>注意事项：</strong></p>
        <ol>
            <li>请根据实际情况填写著作权人信息中的空白部分</li>
            <li>开发完成时间和发表时间请根据实际情况调整</li>
            <li>首次发表地点请填写具体的省市信息</li>
            <li>如需修改任何技术信息，请确保与实际软件功能相符</li>
            <li>本文档可直接在Microsoft Word中打开并另存为.docx格式</li>
        </ol>
    </div>
</body>
</html>
