"""
实时录音模块
支持实时录音和转录功能
"""

import os
import time
import threading
import tempfile
from typing import Optional, Callable
import sounddevice as sd
import soundfile as sf
import numpy as np
from collections import deque
import queue

from ..recognition.engine import RecognitionEngine


class RealtimeRecorder:
    """实时录音和转录器"""
    
    def __init__(self, engine: str = 'whisper', language: str = 'zh-CN',
                 sample_rate: int = 16000, channels: int = 1,
                 chunk_duration: int = 5, overlap_duration: int = 1):
        """
        初始化实时录音器
        
        Args:
            engine: 语音识别引擎
            language: 语言代码
            sample_rate: 采样率
            channels: 声道数
            chunk_duration: 音频块时长（秒）
            overlap_duration: 重叠时长（秒）
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.chunk_duration = chunk_duration
        self.overlap_duration = overlap_duration
        self.chunk_samples = chunk_duration * sample_rate
        self.overlap_samples = overlap_duration * sample_rate
        
        # 语音识别引擎
        self.recognition_engine = RecognitionEngine(engine, language)
        
        # 录音状态
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.transcript_queue = queue.Queue()
        
        # 音频缓冲区
        self.audio_buffer = deque(maxlen=self.chunk_samples + self.overlap_samples)
        
        # 临时文件目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 回调函数
        self.on_transcript = None
        self.on_error = None
    
    def set_callbacks(self, on_transcript: Optional[Callable] = None,
                     on_error: Optional[Callable] = None):
        """
        设置回调函数
        
        Args:
            on_transcript: 转录结果回调函数
            on_error: 错误回调函数
        """
        self.on_transcript = on_transcript
        self.on_error = on_error
    
    def start_recording(self) -> str:
        """
        开始实时录音和转录
        
        Returns:
            完整的转录文本
        """
        print("🎙️ 开始实时录音...")
        print("按 Ctrl+C 停止录音")
        
        self.is_recording = True
        full_transcript = []
        
        # 启动录音线程
        recording_thread = threading.Thread(target=self._recording_worker)
        recording_thread.daemon = True
        recording_thread.start()
        
        # 启动转录线程
        transcription_thread = threading.Thread(target=self._transcription_worker)
        transcription_thread.daemon = True
        transcription_thread.start()
        
        try:
            # 主循环：处理转录结果
            while self.is_recording:
                try:
                    # 获取转录结果
                    transcript = self.transcript_queue.get(timeout=1.0)
                    
                    if transcript.strip():
                        print(f"📝 {transcript}")
                        full_transcript.append(transcript)
                        
                        # 调用回调函数
                        if self.on_transcript:
                            self.on_transcript(transcript)
                    
                except queue.Empty:
                    continue
                except KeyboardInterrupt:
                    break
        
        except KeyboardInterrupt:
            print("\n⏹️ 停止录音...")
        
        finally:
            self.stop_recording()
        
        return '\n'.join(full_transcript)
    
    def stop_recording(self):
        """停止录音"""
        self.is_recording = False
        
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except Exception:
            pass
    
    def _recording_worker(self):
        """录音工作线程"""
        def audio_callback(indata, frames, time, status):
            if status:
                print(f"录音状态: {status}")
            
            # 将音频数据添加到缓冲区
            audio_data = indata[:, 0] if self.channels == 1 else indata
            self.audio_buffer.extend(audio_data.flatten())
            
            # 当缓冲区满时，发送音频块进行转录
            if len(self.audio_buffer) >= self.chunk_samples:
                # 提取音频块
                chunk_data = np.array(list(self.audio_buffer)[:self.chunk_samples])
                
                # 移除已处理的数据（保留重叠部分）
                for _ in range(self.chunk_samples - self.overlap_samples):
                    if self.audio_buffer:
                        self.audio_buffer.popleft()
                
                # 发送到转录队列
                try:
                    self.audio_queue.put_nowait(chunk_data)
                except queue.Full:
                    # 队列满时丢弃最旧的数据
                    try:
                        self.audio_queue.get_nowait()
                        self.audio_queue.put_nowait(chunk_data)
                    except queue.Empty:
                        pass
        
        try:
            # 开始录音
            with sd.InputStream(
                samplerate=self.sample_rate,
                channels=self.channels,
                callback=audio_callback,
                blocksize=1024,
                dtype=np.float32
            ):
                while self.is_recording:
                    time.sleep(0.1)
        
        except Exception as e:
            error_msg = f"录音错误: {str(e)}"
            print(error_msg)
            if self.on_error:
                self.on_error(error_msg)
    
    def _transcription_worker(self):
        """转录工作线程"""
        chunk_counter = 0
        
        while self.is_recording:
            try:
                # 获取音频块
                audio_chunk = self.audio_queue.get(timeout=1.0)
                
                # 保存为临时文件
                chunk_file = os.path.join(self.temp_dir, f"chunk_{chunk_counter:04d}.wav")
                sf.write(chunk_file, audio_chunk, self.sample_rate)
                
                # 转录音频
                try:
                    transcript = self.recognition_engine.transcribe(chunk_file)
                    
                    # 发送转录结果
                    if transcript.strip():
                        self.transcript_queue.put(transcript)
                
                except Exception as e:
                    error_msg = f"转录错误: {str(e)}"
                    print(error_msg)
                    if self.on_error:
                        self.on_error(error_msg)
                
                # 清理临时文件
                try:
                    os.remove(chunk_file)
                except Exception:
                    pass
                
                chunk_counter += 1
            
            except queue.Empty:
                continue
            except Exception as e:
                error_msg = f"转录线程错误: {str(e)}"
                print(error_msg)
                if self.on_error:
                    self.on_error(error_msg)
    
    def get_audio_devices(self) -> list:
        """获取可用的音频设备列表"""
        try:
            devices = sd.query_devices()
            input_devices = []
            
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append({
                        'id': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate']
                    })
            
            return input_devices
        
        except Exception as e:
            print(f"获取音频设备失败: {e}")
            return []
    
    def set_input_device(self, device_id: int):
        """设置输入设备"""
        try:
            sd.default.device[0] = device_id  # 设置默认输入设备
        except Exception as e:
            raise RuntimeError(f"设置输入设备失败: {str(e)}")
    
    def test_microphone(self, duration: int = 3) -> bool:
        """
        测试麦克风
        
        Args:
            duration: 测试时长（秒）
            
        Returns:
            测试是否成功
        """
        try:
            print(f"🎤 测试麦克风 {duration} 秒...")
            
            # 录制测试音频
            test_audio = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=self.channels,
                dtype=np.float32
            )
            sd.wait()  # 等待录制完成
            
            # 检查音频是否有信号
            max_amplitude = np.max(np.abs(test_audio))
            
            if max_amplitude > 0.001:  # 阈值
                print(f"✅ 麦克风测试成功，最大振幅: {max_amplitude:.4f}")
                return True
            else:
                print("❌ 麦克风测试失败，未检测到音频信号")
                return False
        
        except Exception as e:
            print(f"❌ 麦克风测试失败: {str(e)}")
            return False
