"""
语音识别引擎测试
"""

import unittest
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.recognition.engine import RecognitionEngine, WhisperEngine, GoogleEngine, AzureEngine


class TestRecognitionEngine(unittest.TestCase):
    """语音识别引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = RecognitionEngine()
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        self.assertIsInstance(self.engine, RecognitionEngine)
        self.assertIn(self.engine.current_engine, ['whisper', 'google', 'azure'])
    
    def test_get_available_engines(self):
        """测试获取可用引擎"""
        available_engines = self.engine.get_available_engines()
        self.assertIsInstance(available_engines, list)
        # 至少应该有一个可用的引擎
        self.assertGreater(len(available_engines), 0)
    
    def test_set_engine(self):
        """测试设置引擎"""
        original_engine = self.engine.current_engine
        
        # 测试设置有效引擎
        if 'whisper' in self.engine.engines:
            self.engine.set_engine('whisper')
            self.assertEqual(self.engine.current_engine, 'whisper')
        
        # 测试设置无效引擎
        with self.assertRaises(ValueError):
            self.engine.set_engine('invalid_engine')
        
        # 恢复原始引擎
        self.engine.set_engine(original_engine)
    
    def test_get_engine_info(self):
        """测试获取引擎信息"""
        info = self.engine.get_engine_info()
        self.assertIsInstance(info, dict)
        self.assertIn('current_engine', info)
        self.assertIn('available_engines', info)
        self.assertIn('language', info)
        self.assertIn('fallback_engines', info)


class TestWhisperEngine(unittest.TestCase):
    """Whisper引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = WhisperEngine()
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        self.assertIsInstance(self.engine, WhisperEngine)
    
    def test_is_available(self):
        """测试引擎可用性"""
        # 这个测试依赖于Whisper是否正确安装
        available = self.engine.is_available()
        self.assertIsInstance(available, bool)
    
    def test_language_code_conversion(self):
        """测试语言代码转换"""
        # 测试内部方法
        self.assertEqual(self.engine._convert_language_code('zh-CN'), 'zh')
        self.assertEqual(self.engine._convert_language_code('en-US'), 'en')
        self.assertEqual(self.engine._convert_language_code('ja-JP'), 'ja')


class TestGoogleEngine(unittest.TestCase):
    """Google引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = GoogleEngine()
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        self.assertIsInstance(self.engine, GoogleEngine)
    
    def test_is_available(self):
        """测试引擎可用性"""
        available = self.engine.is_available()
        self.assertIsInstance(available, bool)


class TestAzureEngine(unittest.TestCase):
    """Azure引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine = AzureEngine()
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        self.assertIsInstance(self.engine, AzureEngine)
    
    def test_is_available(self):
        """测试引擎可用性"""
        available = self.engine.is_available()
        self.assertIsInstance(available, bool)


if __name__ == '__main__':
    unittest.main()
