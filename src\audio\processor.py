"""
音频处理模块
支持多种音频格式的转换、预处理和优化
"""

import os
import tempfile
from pathlib import Path
from typing import Optional, Tuple
import librosa
import soundfile as sf
from pydub import AudioSegment
from pydub.effects import normalize, compress_dynamic_range
import numpy as np


class AudioProcessor:
    """音频处理器"""
    
    def __init__(self, target_sample_rate: int = 16000, target_channels: int = 1):
        """
        初始化音频处理器
        
        Args:
            target_sample_rate: 目标采样率
            target_channels: 目标声道数
        """
        self.target_sample_rate = target_sample_rate
        self.target_channels = target_channels
        self.supported_formats = {
            '.mp3', '.wav', '.m4a', '.flac', '.ogg', 
            '.aac', '.wma', '.opus', '.webm'
        }
    
    def is_supported_format(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        return Path(file_path).suffix.lower() in self.supported_formats
    
    def preprocess(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        预处理音频文件
        
        Args:
            input_path: 输入音频文件路径
            output_path: 输出文件路径，如果为None则使用临时文件
            
        Returns:
            处理后的音频文件路径
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"音频文件不存在: {input_path}")
        
        if not self.is_supported_format(input_path):
            raise ValueError(f"不支持的音频格式: {Path(input_path).suffix}")
        
        # 如果没有指定输出路径，使用临时文件
        if output_path is None:
            temp_dir = tempfile.mkdtemp()
            output_path = os.path.join(temp_dir, "processed_audio.wav")
        
        try:
            # 使用pydub加载音频
            audio = AudioSegment.from_file(input_path)
            
            # 转换为目标格式
            audio = self._convert_format(audio)
            
            # 音频增强
            audio = self._enhance_audio(audio)
            
            # 导出处理后的音频
            audio.export(output_path, format="wav")
            
            return output_path
            
        except Exception as e:
            raise RuntimeError(f"音频预处理失败: {str(e)}")
    
    def _convert_format(self, audio: AudioSegment) -> AudioSegment:
        """转换音频格式"""
        # 转换采样率
        if audio.frame_rate != self.target_sample_rate:
            audio = audio.set_frame_rate(self.target_sample_rate)
        
        # 转换声道数
        if audio.channels != self.target_channels:
            if self.target_channels == 1:
                audio = audio.set_channels(1)  # 转为单声道
            elif self.target_channels == 2 and audio.channels == 1:
                audio = audio.set_channels(2)  # 转为立体声
        
        return audio
    
    def _enhance_audio(self, audio: AudioSegment) -> AudioSegment:
        """音频增强处理"""
        # 音量标准化
        audio = normalize(audio)
        
        # 动态范围压缩
        audio = compress_dynamic_range(audio)
        
        return audio
    
    def reduce_noise(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        降噪处理（使用librosa）
        
        Args:
            input_path: 输入音频文件路径
            output_path: 输出文件路径
            
        Returns:
            降噪后的音频文件路径
        """
        if output_path is None:
            temp_dir = tempfile.mkdtemp()
            output_path = os.path.join(temp_dir, "denoised_audio.wav")
        
        try:
            # 加载音频
            y, sr = librosa.load(input_path, sr=self.target_sample_rate)
            
            # 简单的谱减法降噪
            y_denoised = self._spectral_subtraction(y, sr)
            
            # 保存降噪后的音频
            sf.write(output_path, y_denoised, sr)
            
            return output_path
            
        except Exception as e:
            raise RuntimeError(f"降噪处理失败: {str(e)}")
    
    def _spectral_subtraction(self, y: np.ndarray, sr: int, 
                            noise_factor: float = 0.1) -> np.ndarray:
        """
        简单的谱减法降噪
        
        Args:
            y: 音频信号
            sr: 采样率
            noise_factor: 噪声因子
            
        Returns:
            降噪后的音频信号
        """
        # 计算短时傅里叶变换
        stft = librosa.stft(y)
        magnitude = np.abs(stft)
        phase = np.angle(stft)
        
        # 估计噪声谱（使用前几帧作为噪声估计）
        noise_frames = min(10, magnitude.shape[1] // 10)
        noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
        
        # 谱减法
        clean_magnitude = magnitude - noise_factor * noise_spectrum
        clean_magnitude = np.maximum(clean_magnitude, 
                                   0.1 * magnitude)  # 避免过度减法
        
        # 重构音频
        clean_stft = clean_magnitude * np.exp(1j * phase)
        y_clean = librosa.istft(clean_stft)
        
        return y_clean
    
    def split_audio(self, input_path: str, chunk_duration: int = 300) -> list:
        """
        将长音频分割成小段
        
        Args:
            input_path: 输入音频文件路径
            chunk_duration: 每段时长（秒）
            
        Returns:
            分割后的音频文件路径列表
        """
        audio = AudioSegment.from_file(input_path)
        chunk_length_ms = chunk_duration * 1000
        
        chunks = []
        temp_dir = tempfile.mkdtemp()
        
        for i, chunk_start_ms in enumerate(range(0, len(audio), chunk_length_ms)):
            chunk_end_ms = min(chunk_start_ms + chunk_length_ms, len(audio))
            chunk = audio[chunk_start_ms:chunk_end_ms]
            
            chunk_path = os.path.join(temp_dir, f"chunk_{i:03d}.wav")
            chunk.export(chunk_path, format="wav")
            chunks.append(chunk_path)
        
        return chunks
    
    def get_audio_info(self, file_path: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            音频信息字典
        """
        try:
            audio = AudioSegment.from_file(file_path)
            
            return {
                'duration': len(audio) / 1000.0,  # 秒
                'sample_rate': audio.frame_rate,
                'channels': audio.channels,
                'format': Path(file_path).suffix.lower(),
                'file_size': os.path.getsize(file_path),
                'bitrate': audio.frame_rate * audio.sample_width * 8 * audio.channels
            }
        except Exception as e:
            raise RuntimeError(f"获取音频信息失败: {str(e)}")
