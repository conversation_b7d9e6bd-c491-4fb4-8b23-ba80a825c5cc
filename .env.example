# 语音识别服务API密钥配置
# 复制此文件为 .env 并填入你的API密钥

# OpenAI API (用于Whisper API调用，可选)
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud Speech-to-Text
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json
GOOGLE_CLOUD_PROJECT_ID=your_project_id

# Azure Cognitive Services Speech
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region

# AWS Transcribe
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1

# 默认配置
DEFAULT_LANGUAGE=zh-CN
DEFAULT_ENGINE=whisper
DEFAULT_OUTPUT_FORMAT=txt

# 音频处理配置
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
NOISE_REDUCTION=true

# 实时录音配置
REALTIME_CHUNK_DURATION=5  # 秒
REALTIME_OVERLAP=1  # 秒

# 文本后处理配置
AUTO_PUNCTUATION=true
SPEAKER_DETECTION=false
TEXT_FORMATTING=true
