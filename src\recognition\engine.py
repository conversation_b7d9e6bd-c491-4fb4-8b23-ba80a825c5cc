"""
语音识别引擎模块
集成多个语音识别服务
"""

import os
import tempfile
from typing import Optional, Dict, List
from abc import ABC, abstractmethod
import whisper
import speech_recognition as sr
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class BaseRecognitionEngine(ABC):
    """语音识别引擎基类"""
    
    @abstractmethod
    def transcribe(self, audio_path: str, language: str = 'zh-CN') -> str:
        """转录音频文件"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查引擎是否可用"""
        pass


class WhisperEngine(BaseRecognitionEngine):
    """OpenAI Whisper引擎"""
    
    def __init__(self, model_size: str = 'base'):
        """
        初始化Whisper引擎
        
        Args:
            model_size: 模型大小 ('tiny', 'base', 'small', 'medium', 'large')
        """
        self.model_size = model_size
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """加载Whisper模型"""
        try:
            self.model = whisper.load_model(self.model_size)
        except Exception as e:
            print(f"Whisper模型加载失败: {e}")
            self.model = None
    
    def transcribe(self, audio_path: str, language: str = 'zh-CN') -> str:
        """使用Whisper转录音频"""
        if not self.model:
            raise RuntimeError("Whisper模型未加载")
        
        try:
            # 将语言代码转换为Whisper格式
            whisper_lang = self._convert_language_code(language)
            
            # 转录音频
            result = self.model.transcribe(
                audio_path,
                language=whisper_lang,
                task='transcribe'
            )
            
            return result['text'].strip()
            
        except Exception as e:
            raise RuntimeError(f"Whisper转录失败: {str(e)}")
    
    def _convert_language_code(self, language: str) -> str:
        """转换语言代码为Whisper格式"""
        lang_map = {
            'zh-CN': 'zh',
            'zh-TW': 'zh',
            'en-US': 'en',
            'en-GB': 'en',
            'ja-JP': 'ja',
            'ko-KR': 'ko',
            'fr-FR': 'fr',
            'de-DE': 'de',
            'es-ES': 'es',
            'ru-RU': 'ru'
        }
        return lang_map.get(language, language.split('-')[0])
    
    def is_available(self) -> bool:
        """检查Whisper是否可用"""
        return self.model is not None


class GoogleEngine(BaseRecognitionEngine):
    """Google Speech-to-Text引擎"""
    
    def __init__(self):
        """初始化Google引擎"""
        self.recognizer = sr.Recognizer()
        self.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    
    def transcribe(self, audio_path: str, language: str = 'zh-CN') -> str:
        """使用Google Speech-to-Text转录音频"""
        try:
            # 加载音频文件
            with sr.AudioFile(audio_path) as source:
                audio = self.recognizer.record(source)
            
            # 使用Google API转录
            text = self.recognizer.recognize_google(
                audio,
                language=language,
                key=None  # 使用默认API密钥
            )
            
            return text
            
        except sr.UnknownValueError:
            return ""
        except sr.RequestError as e:
            raise RuntimeError(f"Google API请求失败: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"Google转录失败: {str(e)}")
    
    def is_available(self) -> bool:
        """检查Google引擎是否可用"""
        try:
            # 简单测试API连接
            return True  # 基础版本总是可用
        except Exception:
            return False


class AzureEngine(BaseRecognitionEngine):
    """Azure Cognitive Services Speech引擎"""
    
    def __init__(self):
        """初始化Azure引擎"""
        self.speech_key = os.getenv('AZURE_SPEECH_KEY')
        self.speech_region = os.getenv('AZURE_SPEECH_REGION')
        self.speech_config = None
        
        if self.speech_key and self.speech_region:
            try:
                import azure.cognitiveservices.speech as speechsdk
                self.speech_config = speechsdk.SpeechConfig(
                    subscription=self.speech_key,
                    region=self.speech_region
                )
                self.speechsdk = speechsdk
            except ImportError:
                print("Azure Speech SDK未安装")
    
    def transcribe(self, audio_path: str, language: str = 'zh-CN') -> str:
        """使用Azure Speech转录音频"""
        if not self.speech_config:
            raise RuntimeError("Azure Speech配置未初始化")
        
        try:
            # 设置语言
            self.speech_config.speech_recognition_language = language
            
            # 创建音频配置
            audio_config = self.speechsdk.audio.AudioConfig(filename=audio_path)
            
            # 创建识别器
            speech_recognizer = self.speechsdk.SpeechRecognizer(
                speech_config=self.speech_config,
                audio_config=audio_config
            )
            
            # 执行识别
            result = speech_recognizer.recognize_once()
            
            if result.reason == self.speechsdk.ResultReason.RecognizedSpeech:
                return result.text
            elif result.reason == self.speechsdk.ResultReason.NoMatch:
                return ""
            else:
                raise RuntimeError(f"Azure识别失败: {result.reason}")
                
        except Exception as e:
            raise RuntimeError(f"Azure转录失败: {str(e)}")
    
    def is_available(self) -> bool:
        """检查Azure引擎是否可用"""
        return self.speech_config is not None


class RecognitionEngine:
    """语音识别引擎管理器"""
    
    def __init__(self, engine: str = 'whisper', language: str = 'zh-CN'):
        """
        初始化识别引擎
        
        Args:
            engine: 引擎类型 ('whisper', 'google', 'azure')
            language: 语言代码
        """
        self.language = language
        self.engines = {
            'whisper': WhisperEngine(),
            'google': GoogleEngine(),
            'azure': AzureEngine(),
        }
        
        self.current_engine = engine
        self.fallback_engines = ['whisper', 'google', 'azure']
    
    def transcribe(self, audio_path: str, speaker_detection: bool = False) -> str:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            speaker_detection: 是否启用说话人识别
            
        Returns:
            转录文本
        """
        # 尝试主引擎
        try:
            engine = self.engines[self.current_engine]
            if engine.is_available():
                result = engine.transcribe(audio_path, self.language)
                if result.strip():
                    return self._post_process(result, speaker_detection)
        except Exception as e:
            print(f"主引擎 {self.current_engine} 失败: {e}")
        
        # 尝试备用引擎
        for engine_name in self.fallback_engines:
            if engine_name == self.current_engine:
                continue
                
            try:
                engine = self.engines[engine_name]
                if engine.is_available():
                    print(f"尝试备用引擎: {engine_name}")
                    result = engine.transcribe(audio_path, self.language)
                    if result.strip():
                        return self._post_process(result, speaker_detection)
            except Exception as e:
                print(f"备用引擎 {engine_name} 失败: {e}")
                continue
        
        raise RuntimeError("所有语音识别引擎都失败了")
    
    def _post_process(self, text: str, speaker_detection: bool = False) -> str:
        """后处理转录文本"""
        # 基本清理
        text = text.strip()
        
        # 如果启用说话人识别，这里可以添加相关逻辑
        if speaker_detection:
            # 简单的说话人标记（实际实现需要更复杂的算法）
            text = self._add_speaker_labels(text)
        
        return text
    
    def _add_speaker_labels(self, text: str) -> str:
        """添加说话人标签（简化版本）"""
        # 这是一个简化的实现，实际的说话人识别需要更复杂的算法
        sentences = text.split('。')
        labeled_text = []
        
        current_speaker = 1
        for i, sentence in enumerate(sentences):
            if sentence.strip():
                # 简单规则：每隔几句话切换说话人
                if i > 0 and i % 3 == 0:
                    current_speaker = 2 if current_speaker == 1 else 1
                
                labeled_text.append(f"说话人{current_speaker}: {sentence.strip()}。")
        
        return '\n'.join(labeled_text)
    
    def get_available_engines(self) -> List[str]:
        """获取可用的引擎列表"""
        available = []
        for name, engine in self.engines.items():
            if engine.is_available():
                available.append(name)
        return available
    
    def set_engine(self, engine: str):
        """设置当前引擎"""
        if engine in self.engines:
            self.current_engine = engine
        else:
            raise ValueError(f"不支持的引擎: {engine}")
    
    def get_engine_info(self) -> Dict:
        """获取引擎信息"""
        return {
            'current_engine': self.current_engine,
            'available_engines': self.get_available_engines(),
            'language': self.language,
            'fallback_engines': self.fallback_engines
        }
