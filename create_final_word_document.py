#!/usr/bin/env python3
"""
生成包含图像的Word文档
使用matplotlib生成流程图图像并嵌入Word文档
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from io import BytesIO
import os

def create_architecture_diagram():
    """创建系统架构图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 定义颜色
    main_color = '#4A90E2'
    module_color = '#7ED321'
    method_color = '#F5A623'
    
    # 主系统
    main_box = FancyBboxPatch((1, 10), 8, 1.5, 
                              boxstyle="round,pad=0.1", 
                              facecolor=main_color, 
                              edgecolor='black', 
                              linewidth=2)
    ax.add_patch(main_box)
    ax.text(5, 10.75, 'TranscriberSystem (主系统)', 
            ha='center', va='center', fontsize=14, fontweight='bold', color='white')
    
    # 模块位置
    modules = [
        ('AudioProcessor\n(音频处理器)', 0.5, 8),
        ('VideoProcessor\n(视频处理器)', 2.5, 8),
        ('OnlineDownloader\n(在线下载器)', 4.5, 8),
        ('RecognitionEngine\n(语音识别引擎)', 6.5, 8),
        ('RealtimeRecorder\n(实时录音器)', 0.5, 5.5),
        ('TextFormatter\n(文本格式化器)', 2.5, 5.5),
        ('UserInterface\n(用户界面)', 4.5, 5.5)
    ]
    
    # 绘制模块
    for name, x, y in modules:
        box = FancyBboxPatch((x, y), 1.8, 1.2, 
                            boxstyle="round,pad=0.05", 
                            facecolor=module_color, 
                            edgecolor='black', 
                            linewidth=1)
        ax.add_patch(box)
        ax.text(x + 0.9, y + 0.6, name, 
                ha='center', va='center', fontsize=9, fontweight='bold')
        
        # 连接线到主系统
        ax.plot([x + 0.9, 5], [y + 1.2, 10], 'k-', linewidth=1, alpha=0.6)
    
    # 添加方法示例（AudioProcessor）
    methods = [
        'preprocess()', 'reduceNoise()', 'splitAudio()', 'getAudioInfo()'
    ]
    for i, method in enumerate(methods):
        method_box = FancyBboxPatch((0.1, 6.8 - i*0.4), 1.2, 0.3, 
                                   boxstyle="round,pad=0.02", 
                                   facecolor=method_color, 
                                   edgecolor='gray', 
                                   linewidth=0.5)
        ax.add_patch(method_box)
        ax.text(0.7, 6.95 - i*0.4, method, 
                ha='center', va='center', fontsize=7)
        
        # 连接线
        ax.plot([1.3, 1.4], [6.95 - i*0.4, 8.6], 'k-', linewidth=0.5, alpha=0.5)
    
    # 子引擎（RecognitionEngine）
    sub_engines = ['WhisperEngine', 'GoogleEngine', 'AzureEngine']
    for i, engine in enumerate(sub_engines):
        sub_box = FancyBboxPatch((7.2, 7.5 - i*0.4), 1.5, 0.3, 
                                boxstyle="round,pad=0.02", 
                                facecolor='#BD10E0', 
                                edgecolor='gray', 
                                linewidth=0.5)
        ax.add_patch(sub_box)
        ax.text(7.95, 7.65 - i*0.4, engine, 
                ha='center', va='center', fontsize=7, color='white')
        
        # 连接线
        ax.plot([7.2, 7.4], [7.65 - i*0.4, 8.6], 'k-', linewidth=0.5, alpha=0.5)
    
    plt.title('软件系统架构图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    
    # 保存图像
    img_buffer = BytesIO()
    plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
    img_buffer.seek(0)
    plt.close()
    
    return img_buffer

def create_flowchart_diagram():
    """创建流程图"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 14))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 16)
    ax.axis('off')
    
    # 定义流程步骤
    steps = [
        ('开始', 5, 15, '#4A90E2'),
        ('选择输入类型', 5, 13.5, '#F5A623'),
        ('音频文件', 1.5, 12, '#7ED321'),
        ('视频文件', 3.5, 12, '#7ED321'),
        ('在线链接', 5.5, 12, '#7ED321'),
        ('实时录音', 7.5, 12, '#7ED321'),
        ('音频预处理', 1.5, 10.5, '#50E3C2'),
        ('提取音频', 3.5, 10.5, '#50E3C2'),
        ('下载内容', 5.5, 10.5, '#50E3C2'),
        ('实时录制', 7.5, 10.5, '#50E3C2'),
        ('语音识别引擎', 5, 9, '#BD10E0'),
        ('Whisper', 2.5, 7.5, '#D0021B'),
        ('Google', 5, 7.5, '#D0021B'),
        ('Azure', 7.5, 7.5, '#D0021B'),
        ('文本后处理', 5, 6, '#F8E71C'),
        ('标点添加', 2, 4.5, '#B8E986'),
        ('说话人识别', 4, 4.5, '#B8E986'),
        ('段落划分', 6, 4.5, '#B8E986'),
        ('格式优化', 8, 4.5, '#B8E986'),
        ('输出模块', 5, 3, '#4A90E2'),
        ('TXT', 2.5, 1.5, '#9013FE'),
        ('Markdown', 5, 1.5, '#9013FE'),
        ('HTML', 7.5, 1.5, '#9013FE'),
        ('完成', 5, 0.5, '#4A90E2')
    ]
    
    # 绘制步骤
    boxes = {}
    for name, x, y, color in steps:
        if name in ['开始', '完成']:
            # 圆形
            circle = plt.Circle((x, y), 0.4, facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(circle)
        elif name == '选择输入类型':
            # 菱形
            diamond = patches.RegularPolygon((x, y), 4, radius=0.6, 
                                           orientation=np.pi/4, 
                                           facecolor=color, 
                                           edgecolor='black', 
                                           linewidth=2)
            ax.add_patch(diamond)
        else:
            # 矩形
            box = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6, 
                               boxstyle="round,pad=0.05", 
                               facecolor=color, 
                               edgecolor='black', 
                               linewidth=1)
            ax.add_patch(box)
        
        # 文字
        ax.text(x, y, name, ha='center', va='center', 
                fontsize=8, fontweight='bold', 
                color='white' if color in ['#4A90E2', '#BD10E0', '#D0021B'] else 'black')
        
        boxes[name] = (x, y)
    
    # 绘制连接线
    connections = [
        ('开始', '选择输入类型'),
        ('选择输入类型', '音频文件'),
        ('选择输入类型', '视频文件'),
        ('选择输入类型', '在线链接'),
        ('选择输入类型', '实时录音'),
        ('音频文件', '音频预处理'),
        ('视频文件', '提取音频'),
        ('在线链接', '下载内容'),
        ('实时录音', '实时录制'),
        ('音频预处理', '语音识别引擎'),
        ('提取音频', '语音识别引擎'),
        ('下载内容', '语音识别引擎'),
        ('实时录制', '语音识别引擎'),
        ('语音识别引擎', 'Whisper'),
        ('语音识别引擎', 'Google'),
        ('语音识别引擎', 'Azure'),
        ('Whisper', '文本后处理'),
        ('Google', '文本后处理'),
        ('Azure', '文本后处理'),
        ('文本后处理', '标点添加'),
        ('文本后处理', '说话人识别'),
        ('文本后处理', '段落划分'),
        ('文本后处理', '格式优化'),
        ('标点添加', '输出模块'),
        ('说话人识别', '输出模块'),
        ('段落划分', '输出模块'),
        ('格式优化', '输出模块'),
        ('输出模块', 'TXT'),
        ('输出模块', 'Markdown'),
        ('输出模块', 'HTML'),
        ('TXT', '完成'),
        ('Markdown', '完成'),
        ('HTML', '完成')
    ]
    
    for start, end in connections:
        if start in boxes and end in boxes:
            x1, y1 = boxes[start]
            x2, y2 = boxes[end]
            
            # 计算箭头
            dx = x2 - x1
            dy = y2 - y1
            length = np.sqrt(dx**2 + dy**2)
            
            if length > 0:
                # 调整起点和终点，避免与框重叠
                offset = 0.4
                x1_adj = x1 + (dx/length) * offset
                y1_adj = y1 + (dy/length) * offset
                x2_adj = x2 - (dx/length) * offset
                y2_adj = y2 - (dy/length) * offset
                
                ax.annotate('', xy=(x2_adj, y2_adj), xytext=(x1_adj, y1_adj),
                           arrowprops=dict(arrowstyle='->', color='black', lw=1))
    
    plt.title('软件处理流程图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    
    # 保存图像
    img_buffer = BytesIO()
    plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
    img_buffer.seek(0)
    plt.close()
    
    return img_buffer

def create_word_document_with_images():
    """创建包含图像的Word文档"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        
        # 创建文档
        doc = Document()
        
        # 设置文档标题
        title = doc.add_heading('软件著作权登记信息表', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加填表说明
        doc.add_heading("填表说明", level=1)
        doc.add_paragraph("1. 著作权人信息：")
        doc.add_paragraph("   • 名称：公司申请填写公司全称，个人申请填写个人名字")
        doc.add_paragraph("   • 地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址")
        doc.add_paragraph("   • 证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码")
        
        doc.add_paragraph("2. 软件信息：")
        doc.add_paragraph("   • 软件的全称应该以'软件、系统、平台'三词之一结尾，简称应当与全称相关，比全称短")
        doc.add_paragraph("   • 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后")
        
        # 一、著作权人基础信息
        doc.add_heading("一、著作权人基础信息", level=2)
        
        table1 = doc.add_table(rows=4, cols=2)
        table1.style = 'Table Grid'
        
        table1.cell(0, 0).text = "名称"
        table1.cell(0, 1).text = "【请填写著作权人姓名/公司名称】"
        table1.cell(1, 0).text = "地址"
        table1.cell(1, 1).text = "【请填写实名认证时所填写的省市地址】"
        table1.cell(2, 0).text = "成立时间"
        table1.cell(2, 1).text = "【请填写成立时间】"
        table1.cell(3, 0).text = "统一信用代码"
        table1.cell(3, 1).text = "【公司填写统一信用代码，个人填写身份证号码】"
        
        # 二、软件基本信息
        doc.add_heading("二、软件基本信息", level=2)
        
        table2 = doc.add_table(rows=7, cols=2)
        table2.style = 'Table Grid'
        
        software_data = [
            ("权利取得方式", "☑ 原始取得  ☐ 继受取得"),
            ("全称", "智能会议记录转文字稿软件"),
            ("简称", "会议转录软件"),
            ("版本号", "V1.0"),
            ("权利范围", "☑ 全部  ☐ 部分权利"),
            ("软件分类", "☑ 应用软件  ☐ 嵌入式软件  ☐ 中间件  ☐ 操作系统"),
            ("软件说明", "☑ 原创  ☐ 修改（升级版本）")
        ]
        
        for i, (key, value) in enumerate(software_data):
            table2.cell(i, 0).text = key
            table2.cell(i, 1).text = value
        
        # 三、开发信息
        doc.add_heading("三、开发信息", level=2)
        
        table3 = doc.add_table(rows=5, cols=2)
        table3.style = 'Table Grid'
        
        dev_data = [
            ("开发方式", "☑ 独立开发  ☐ 合作开发  ☐ 委托开发  ☐ 下达任务开发"),
            ("开发完成时间", "2025年07月29日"),
            ("发表状态", "☑ 已发表  ☐ 未发表"),
            ("首次发表时间", "2025年07月29日"),
            ("首次发表地点", "【请填写具体省市】")
        ]
        
        for i, (key, value) in enumerate(dev_data):
            table3.cell(i, 0).text = key
            table3.cell(i, 1).text = value
        
        # 四、软件系统架构图
        doc.add_heading("四、软件系统架构图", level=2)
        
        print("正在生成系统架构图...")
        arch_img = create_architecture_diagram()
        
        # 添加图像
        paragraph = doc.add_paragraph()
        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
        run.add_picture(arch_img, width=Inches(6))
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 五、软件处理流程图
        doc.add_heading("五、软件处理流程图", level=2)
        
        print("正在生成流程图...")
        flow_img = create_flowchart_diagram()
        
        # 添加图像
        paragraph = doc.add_paragraph()
        run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
        run.add_picture(flow_img, width=Inches(6))
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 六、技术环境信息
        doc.add_heading("六、技术环境信息", level=2)
        
        table4 = doc.add_table(rows=8, cols=2)
        table4.style = 'Table Grid'
        
        tech_data = [
            ("开发的硬件环境", "Intel i5以上CPU，8GB内存，100GB硬盘"),
            ("运行的硬件环境", "Intel i3以上CPU，4GB内存，50GB硬盘"),
            ("开发该软件的操作系统", "Windows 10/11, macOS 10.15+, Ubuntu 18.04+"),
            ("软件开发环境/开发工具", "Python 3.8+, VS Code, Git"),
            ("该软件的运行平台/操作系统", "Windows, macOS, Linux"),
            ("软件运行支撑环境/支持软件", "Python运行环境, FFmpeg, 浏览器"),
            ("编程语言", "Python, HTML, CSS, JavaScript"),
            ("源程序量", "约8000行")
        ]
        
        for i, (key, value) in enumerate(tech_data):
            table4.cell(i, 0).text = key
            table4.cell(i, 1).text = value
        
        # 七、功能描述
        doc.add_heading("七、功能描述", level=2)
        
        table5 = doc.add_table(rows=4, cols=2)
        table5.style = 'Table Grid'
        
        func_data = [
            ("开发目的", "为会议记录和音视频转录提供智能化解决方案"),
            ("面向领域/行业", "办公自动化、教育培训、媒体制作、企业服务"),
            ("软件的主要功能", "支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。"),
            ("软件的技术特点", "集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。")
        ]
        
        for i, (key, value) in enumerate(func_data):
            table5.cell(i, 0).text = key
            table5.cell(i, 1).text = value
        
        # 八、核心功能模块详述
        doc.add_heading("八、核心功能模块详述", level=2)
        
        modules = [
            ("1. 音频处理模块", [
                "支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式",
                "音频预处理：格式转换、采样率调整、声道转换",
                "音频增强：音量标准化、动态范围压缩",
                "降噪处理：基于谱减法的噪声抑制"
            ]),
            ("2. 语音识别引擎", [
                "OpenAI Whisper本地识别：支持多种模型大小，离线工作",
                "Google Speech-to-Text云端识别：高精度在线识别",
                "Azure认知服务：企业级可靠性",
                "多引擎备选机制：主引擎失败时自动切换"
            ]),
            ("3. 实时录音模块", [
                "实时音频录制：支持麦克风和系统音频",
                "流式转录：边录制边转录，实时显示结果",
                "音频设备管理：自动检测和选择输入设备",
                "音频质量监控：实时监控信号强度"
            ]),
            ("4. 文本后处理模块", [
                "智能标点符号添加：根据语义自动添加标点",
                "说话人识别和标记：简单的说话人分离",
                "文本格式化：段落划分、语句整理",
                "会议摘要生成：提取关键信息和决策要点"
            ])
        ]
        
        for module_name, features in modules:
            doc.add_paragraph(module_name, style='Heading 3')
            for feature in features:
                p = doc.add_paragraph()
                p.add_run("• ").bold = True
                p.add_run(feature)
        
        # 添加注意事项
        doc.add_paragraph()
        p = doc.add_paragraph()
        p.add_run("注意事项：").bold = True
        
        notes = [
            "请根据实际情况填写著作权人信息中的空白部分",
            "开发完成时间和发表时间请根据实际情况调整",
            "首次发表地点请填写具体的省市信息",
            "如需修改任何技术信息，请确保与实际软件功能相符"
        ]
        
        for i, note in enumerate(notes, 1):
            doc.add_paragraph(f"{i}. {note}")
        
        # 保存到桌面
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        output_file = os.path.join(desktop_path, "软件著作权登记申请表_最终版.docx")
        
        doc.save(output_file)
        
        return output_file
        
    except ImportError:
        print("❌ 缺少依赖包。正在尝试安装...")
        import subprocess
        import sys
        
        packages = ['python-docx', 'matplotlib']
        for package in packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            except:
                print(f"❌ 无法安装 {package}")
                return None
        
        print("✅ 依赖包安装完成，请重新运行脚本")
        return None

def main():
    """主函数"""
    print("📄 开始生成包含图像的Word文档...")
    print("🎨 正在生成流程图图像...")
    
    try:
        output_file = create_word_document_with_images()
        
        if output_file:
            print(f"✅ Word文档已生成并保存到桌面！")
            print(f"📁 文件位置: {output_file}")
            print("\n📋 文档包含内容：")
            print("   • 完整的软件著作权登记信息表")
            print("   • 高质量的系统架构图（图像格式）")
            print("   • 专业的软件处理流程图（图像格式）")
            print("   • 详细的功能模块说明")
            print("   • 标准的表格格式")
            print("\n💡 特色：")
            print("   • 使用真正的图像而非文本")
            print("   • 专业的流程图设计")
            print("   • 符合软件著作权登记要求")
            print("   • 可直接用于官方申请")
            
            # 尝试打开文件
            try:
                import subprocess
                subprocess.Popen(['start', output_file], shell=True)
                print("\n📖 文档已自动打开")
            except:
                print(f"\n📖 请手动打开文件: {output_file}")
        else:
            print("❌ 文档生成失败")
            
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {e}")
        print("请确保已安装必要的依赖包：pip install python-docx matplotlib")

if __name__ == "__main__":
    main()
