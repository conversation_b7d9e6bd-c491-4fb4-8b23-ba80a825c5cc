"""
Web用户界面
基于Streamlit的Web界面
"""

import os
import sys
import tempfile
from pathlib import Path
import streamlit as st
from io import BytesIO

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.audio.processor import AudioProcessor
from src.video.processor import VideoProcessor
from src.online.downloader import OnlineDownloader
from src.recognition.engine import RecognitionEngine
from src.postprocess.formatter import TextFormatter


class TranscriberWeb:
    """转录器Web界面"""
    
    def __init__(self):
        """初始化Web界面"""
        self.audio_processor = AudioProcessor()
        self.video_processor = VideoProcessor()
        self.online_downloader = OnlineDownloader()
        self.text_formatter = TextFormatter()
        
        # 设置页面配置
        st.set_page_config(
            page_title="会议记录转文字稿工具",
            page_icon="🎙️",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def render_header(self):
        """渲染页面头部"""
        st.title("🎙️ 会议记录转文字稿工具")
        st.markdown("支持音频、视频、在线链接转录，提供多种语音识别引擎")
        st.divider()
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("⚙️ 设置")
            
            # 语音识别引擎选择
            recognition_engine = RecognitionEngine()
            available_engines = recognition_engine.get_available_engines()
            
            if available_engines:
                engine = st.selectbox(
                    "语音识别引擎",
                    available_engines,
                    index=0,
                    format_func=lambda x: x.title()
                )
            else:
                st.error("没有可用的语音识别引擎")
                engine = "whisper"
            
            # 语言选择
            languages = {
                "zh-CN": "中文（简体）",
                "en-US": "英语（美国）",
                "ja-JP": "日语",
                "ko-KR": "韩语"
            }
            
            language = st.selectbox(
                "识别语言",
                list(languages.keys()),
                format_func=lambda x: languages[x]
            )
            
            st.divider()
            
            # 格式化选项
            st.subheader("📝 文本格式化")
            format_options = {
                'add_punctuation': st.checkbox("添加标点符号", value=True),
                'remove_fillers': st.checkbox("移除填充词", value=False),
                'add_paragraphs': st.checkbox("智能分段", value=True),
                'speaker_detection': st.checkbox("说话人识别", value=False),
                'add_timestamps': st.checkbox("添加时间戳", value=False),
                'add_summary': st.checkbox("生成摘要", value=False)
            }
            
            return engine, language, format_options
    
    def render_file_upload(self, engine: str, language: str, format_options: dict):
        """渲染文件上传界面"""
        st.header("📁 文件上传")
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择音频或视频文件",
            type=['mp3', 'wav', 'm4a', 'flac', 'ogg', 'aac', 
                  'mp4', 'avi', 'mov', 'mkv', 'webm'],
            help="支持常见的音频和视频格式"
        )
        
        if uploaded_file is not None:
            # 显示文件信息
            file_details = {
                "文件名": uploaded_file.name,
                "文件大小": f"{uploaded_file.size / 1024 / 1024:.2f} MB",
                "文件类型": uploaded_file.type
            }
            
            st.json(file_details)
            
            # 处理按钮
            if st.button("🚀 开始转录", type="primary"):
                self._process_uploaded_file(uploaded_file, engine, language, format_options)
    
    def render_online_link(self, engine: str, language: str, format_options: dict):
        """渲染在线链接界面"""
        st.header("🔗 在线链接")
        
        # URL输入
        url = st.text_input(
            "输入视频/音频链接",
            placeholder="https://www.youtube.com/watch?v=...",
            help="支持YouTube、Bilibili等平台"
        )
        
        if url:
            col1, col2 = st.columns([1, 3])
            
            with col1:
                if st.button("📋 获取信息"):
                    self._get_online_info(url)
            
            with col2:
                if st.button("🚀 开始转录", type="primary"):
                    self._process_online_link(url, engine, language, format_options)
    
    def render_realtime_recording(self, engine: str, language: str, format_options: dict):
        """渲染实时录音界面"""
        st.header("🎙️ 实时录音")
        
        st.info("实时录音功能需要在本地环境中运行，Web界面暂不支持。请使用命令行版本。")
        
        # 显示命令行使用方法
        st.code("python main.py --realtime", language="bash")
    
    def _process_uploaded_file(self, uploaded_file, engine: str, language: str, format_options: dict):
        """处理上传的文件"""
        try:
            # 保存上传的文件到临时目录
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                tmp_file.write(uploaded_file.getvalue())
                tmp_file_path = tmp_file.name
            
            # 显示进度
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 预处理
            status_text.text("正在预处理文件...")
            progress_bar.progress(25)
            
            file_extension = uploaded_file.name.split('.')[-1].lower()
            
            if file_extension in ['mp3', 'wav', 'm4a', 'flac', 'ogg', 'aac']:
                # 音频文件
                audio_file = self.audio_processor.preprocess(tmp_file_path)
            else:
                # 视频文件
                audio_file = self.video_processor.extract_audio(tmp_file_path)
            
            progress_bar.progress(50)
            
            # 语音识别
            status_text.text("正在进行语音识别...")
            recognition_engine = RecognitionEngine(engine=engine, language=language)
            transcript = recognition_engine.transcribe(audio_file)
            
            progress_bar.progress(75)
            
            # 文本格式化
            if any(format_options.values()):
                status_text.text("正在格式化文本...")
                transcript = self.text_formatter.format(transcript, format_options)
            
            progress_bar.progress(100)
            status_text.text("转录完成！")
            
            # 显示结果
            self._display_results(transcript, uploaded_file.name)
            
            # 清理临时文件
            os.unlink(tmp_file_path)
            if audio_file != tmp_file_path:
                try:
                    os.unlink(audio_file)
                except:
                    pass
        
        except Exception as e:
            st.error(f"处理失败: {str(e)}")
    
    def _process_online_link(self, url: str, engine: str, language: str, format_options: dict):
        """处理在线链接"""
        try:
            # 显示进度
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # 下载
            status_text.text("正在下载内容...")
            audio_file = self.online_downloader.download(url)
            progress_bar.progress(50)
            
            # 语音识别
            status_text.text("正在进行语音识别...")
            recognition_engine = RecognitionEngine(engine=engine, language=language)
            transcript = recognition_engine.transcribe(audio_file)
            progress_bar.progress(75)
            
            # 文本格式化
            if any(format_options.values()):
                status_text.text("正在格式化文本...")
                transcript = self.text_formatter.format(transcript, format_options)
            
            progress_bar.progress(100)
            status_text.text("转录完成！")
            
            # 显示结果
            self._display_results(transcript, "在线内容")
            
            # 清理临时文件
            try:
                os.unlink(audio_file)
            except:
                pass
        
        except Exception as e:
            st.error(f"处理失败: {str(e)}")
    
    def _get_online_info(self, url: str):
        """获取在线视频信息"""
        try:
            with st.spinner("正在获取视频信息..."):
                info = self.online_downloader.get_video_info(url)
            
            # 显示信息
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**标题:**", info.get('title', '未知'))
                st.write("**时长:**", f"{info.get('duration', 0):.0f} 秒")
                st.write("**上传者:**", info.get('uploader', '未知'))
            
            with col2:
                st.write("**观看次数:**", f"{info.get('view_count', 0):,}")
                st.write("**平台:**", info.get('platform', '未知'))
                st.write("**包含音频:**", "是" if info.get('has_audio') else "否")
        
        except Exception as e:
            st.error(f"获取信息失败: {str(e)}")
    
    def _display_results(self, transcript: str, source_name: str):
        """显示转录结果"""
        st.success("转录完成！")
        
        # 结果展示
        st.subheader("📄 转录结果")
        
        # 文本框显示结果
        st.text_area(
            "转录文本",
            value=transcript,
            height=400,
            help="可以复制文本内容"
        )
        
        # 下载按钮
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 下载TXT
            st.download_button(
                label="📄 下载TXT",
                data=transcript,
                file_name=f"transcript_{source_name}.txt",
                mime="text/plain"
            )
        
        with col2:
            # 下载Markdown
            markdown_content = self.text_formatter._to_markdown(transcript)
            st.download_button(
                label="📝 下载Markdown",
                data=markdown_content,
                file_name=f"transcript_{source_name}.md",
                mime="text/markdown"
            )
        
        with col3:
            # 下载HTML
            html_content = self.text_formatter._to_html(transcript)
            st.download_button(
                label="🌐 下载HTML",
                data=html_content,
                file_name=f"transcript_{source_name}.html",
                mime="text/html"
            )
        
        # 统计信息
        st.subheader("📊 统计信息")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("字符数", len(transcript))
        
        with col2:
            st.metric("词数", len(transcript.split()))
        
        with col3:
            st.metric("行数", len(transcript.split('\n')))
        
        with col4:
            # 估算阅读时间（按每分钟200字计算）
            reading_time = len(transcript) / 200
            st.metric("阅读时间", f"{reading_time:.1f}分钟")
    
    def run(self):
        """运行Web应用"""
        self.render_header()
        
        # 侧边栏设置
        engine, language, format_options = self.render_sidebar()
        
        # 主要内容区域
        tab1, tab2, tab3 = st.tabs(["📁 文件上传", "🔗 在线链接", "🎙️ 实时录音"])
        
        with tab1:
            self.render_file_upload(engine, language, format_options)
        
        with tab2:
            self.render_online_link(engine, language, format_options)
        
        with tab3:
            self.render_realtime_recording(engine, language, format_options)


def main():
    """Web应用主函数"""
    app = TranscriberWeb()
    app.run()


if __name__ == "__main__":
    main()
