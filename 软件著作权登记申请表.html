<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件著作权登记信息表</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 2cm;
            color: #000;
        }
        
        .header {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 15px;
            background-color: #f0f0f0;
            padding: 8px;
            border-left: 4px solid #0066cc;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        
        table, th, td {
            border: 1px solid #000;
        }
        
        th, td {
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            width: 25%;
        }
        
        .checkbox {
            margin-right: 10px;
        }
        
        .note {
            font-size: 10pt;
            color: #666;
            font-style: italic;
            margin-top: 5px;
        }
        
        .fill-blank {
            border-bottom: 1px solid #000;
            min-width: 200px;
            display: inline-block;
            margin: 0 5px;
        }
        
        .instructions {
            background-color: #f9f9f9;
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
            font-size: 11pt;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        @media print {
            body { margin: 1cm; }
            .instructions { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        软件著作权登记信息表
    </div>

    <div class="instructions">
        <h3>填表说明：</h3>
        <p><strong>1. 著作权人信息：</strong></p>
        <ul>
            <li>名称：公司申请填写公司全称，个人申请填写个人名字</li>
            <li>地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址</li>
            <li>证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码</li>
        </ul>
        <p><strong>2. 软件信息：</strong></p>
        <ul>
            <li>软件的全称应该以"软件、系统、平台"三词之一结尾，简称应当与全称相关，比全称短</li>
            <li>软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后</li>
            <li>软件其他信息应该如实填写，并与软件的说明书和源代码相匹配</li>
        </ul>
    </div>

    <div class="section">
        <div class="section-title">著作权人基础信息</div>
        <table>
            <tr>
                <th>名称</th>
                <td><span class="fill-blank" style="width: 400px;"></span></td>
            </tr>
            <tr>
                <th>地址</th>
                <td><span class="fill-blank" style="width: 400px;"></span>
                    <div class="note">注：实名认证时所填写的省市</div>
                </td>
            </tr>
            <tr>
                <th>成立时间</th>
                <td><span class="fill-blank" style="width: 200px;"></span></td>
            </tr>
            <tr>
                <th>统一信用代码</th>
                <td><span class="fill-blank" style="width: 300px;"></span>
                    <div class="note">注：著作权人为个人时，请填写身份证件号码</div>
                </td>
            </tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">软件信息</div>
        <table>
            <tr>
                <th>权利取得方式</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 原始取得
                    <input type="checkbox" class="checkbox"> 继受取得
                </td>
            </tr>
            <tr>
                <th>全称</th>
                <td><strong>智能会议记录转文字稿软件</strong></td>
            </tr>
            <tr>
                <th>简称</th>
                <td><strong>会议转录软件</strong></td>
            </tr>
            <tr>
                <th>版本号</th>
                <td><strong>V1.0</strong></td>
            </tr>
            <tr>
                <th>权利范围</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 全部
                    <input type="checkbox" class="checkbox"> 部分权利
                </td>
            </tr>
            <tr>
                <th>软件分类</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 应用软件
                    <input type="checkbox" class="checkbox"> 嵌入式软件
                    <input type="checkbox" class="checkbox"> 中间件
                    <input type="checkbox" class="checkbox"> 操作系统
                </td>
            </tr>
            <tr>
                <th>软件说明</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 原创
                    <input type="checkbox" class="checkbox"> 修改（升级版本）
                </td>
            </tr>
            <tr>
                <th>开发方式</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 独立开发
                    <input type="checkbox" class="checkbox"> 合作开发
                    <input type="checkbox" class="checkbox"> 委托开发
                    <input type="checkbox" class="checkbox"> 下达任务开发
                </td>
            </tr>
            <tr>
                <th>开发完成时间</th>
                <td><strong>2025年07月29日</strong>
                    <div class="note">注：具体到年月日</div>
                </td>
            </tr>
            <tr>
                <th>发表状态</th>
                <td>
                    <input type="checkbox" class="checkbox" checked> 已发表
                    <input type="checkbox" class="checkbox"> 未发表
                </td>
            </tr>
            <tr>
                <th>首次发表时间</th>
                <td><strong>2025年07月29日</strong>
                    <div class="note">注：具体到年月日</div>
                </td>
            </tr>
            <tr>
                <th>首次发表地点</th>
                <td><span class="fill-blank" style="width: 200px;"></span>
                    <div class="note">注：具体到省市</div>
                </td>
            </tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">技术环境信息</div>
        <table>
            <tr>
                <th>开发的硬件环境</th>
                <td><strong>Intel i5以上CPU，8GB内存，100GB硬盘</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>运行的硬件环境</th>
                <td><strong>Intel i3以上CPU，4GB内存，50GB硬盘</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>开发该软件的操作系统</th>
                <td><strong>Windows 10/11, macOS 10.15+, Ubuntu 18.04+</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>软件开发环境/开发工具</th>
                <td><strong>Python 3.8+, VS Code, Git</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>该软件的运行平台/操作系统</th>
                <td><strong>Windows, macOS, Linux</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>软件运行支撑环境/支持软件</th>
                <td><strong>Python运行环境, FFmpeg, 浏览器</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>编程语言</th>
                <td><strong>Python, HTML, CSS, JavaScript</strong>
                    <div class="note">注：要求120字以内</div>
                </td>
            </tr>
            <tr>
                <th>源程序量</th>
                <td><strong>约8000行</strong>
                    <div class="note">注：按照实际代码量填写</div>
                </td>
            </tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">功能描述</div>
        <table>
            <tr>
                <th>开发目的</th>
                <td><strong>为会议记录和音视频转录提供智能化解决方案</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>面向领域/行业</th>
                <td><strong>办公自动化、教育培训、媒体制作、企业服务</strong>
                    <div class="note">注：要求50字以内</div>
                </td>
            </tr>
            <tr>
                <th>软件的主要功能</th>
                <td><strong>支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。</strong>
                    <div class="note">注：要求200字以内</div>
                </td>
            </tr>
            <tr>
                <th>软件的技术特点</th>
                <td><strong>集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。</strong>
                    <div class="note">注：要求100字以内</div>
                </td>
            </tr>
        </table>
    </div>

    <div style="margin-top: 40px; font-size: 10pt; color: #666;">
        <p><strong>注意事项：</strong></p>
        <ol>
            <li>请根据实际情况填写著作权人信息中的空白部分</li>
            <li>开发完成时间和发表时间请根据实际情况调整</li>
            <li>首次发表地点请填写具体的省市信息</li>
            <li>如需修改任何技术信息，请确保与实际软件功能相符</li>
        </ol>
    </div>
</body>
</html>
