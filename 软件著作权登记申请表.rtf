{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;} {\f1 SimSun;}}
{\colortbl;\red0\green0\blue0;\red0\green0\blue255;}

\f1\fs24\qc\b 软件著作权登记信息表\b0\par
\par

\fs20\ql\b 填表说明：\b0\par
\par
\b 1. 著作权人信息：\b0\par
• 名称：公司申请填写公司全称，个人申请填写个人名字\par
• 地址：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址\par
• 证件号码：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码\par
\par
\b 2. 软件信息：\b0\par
• 软件的全称应该以"软件、系统、平台"三词之一结尾，简称应当与全称相关，比全称短\par
• 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后\par
\par

\b\fs22 一、著作权人基础信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx2000\cellx8000
\b 名称\b0\cell 【请填写著作权人姓名/公司名称】\cell\row
\b 地址\b0\cell 【请填写实名认证时所填写的省市地址】\cell\row
\b 成立时间\b0\cell 【请填写成立时间】\cell\row
\b 统一信用代码\b0\cell 【公司填写统一信用代码，个人填写身份证号码】\cell\row
\par

\b\fs22 二、软件基本信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx2000\cellx8000
\b 权利取得方式\b0\cell ☑ 原始取得  ☐ 继受取得\cell\row
\b 全称\b0\cell 智能会议记录转文字稿软件\cell\row
\b 简称\b0\cell 会议转录软件\cell\row
\b 版本号\b0\cell V1.0\cell\row
\b 权利范围\b0\cell ☑ 全部  ☐ 部分权利\cell\row
\b 软件分类\b0\cell ☑ 应用软件  ☐ 嵌入式软件  ☐ 中间件  ☐ 操作系统\cell\row
\b 软件说明\b0\cell ☑ 原创  ☐ 修改（升级版本）\cell\row
\par

\b\fs22 三、开发信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx2000\cellx8000
\b 开发方式\b0\cell ☑ 独立开发  ☐ 合作开发  ☐ 委托开发  ☐ 下达任务开发\cell\row
\b 开发完成时间\b0\cell 2025年07月29日\cell\row
\b 发表状态\b0\cell ☑ 已发表  ☐ 未发表\cell\row
\b 首次发表时间\b0\cell 2025年07月29日\cell\row
\b 首次发表地点\b0\cell 【请填写具体省市】\cell\row
\par

\b\fs22 四、软件系统架构图\b0\fs20\par
\par
\b 系统类图结构：\b0\par
\par
\f0\fs18
TranscriberSystem (主系统)\par
├── AudioProcessor (音频处理器)\par
│   ├── supportedFormats: String[]\par
│   ├── targetSampleRate: int\par
│   ├── targetChannels: int\par
│   ├── preprocess(filePath): String\par
│   ├── reduceNoise(audioFile): String\par
│   ├── splitAudio(duration): String[]\par
│   └── getAudioInfo(): Dict\par
\par
├── VideoProcessor (视频处理器)\par
│   ├── videoFormats: String[]\par
│   ├── ffmpegAvailable: boolean\par
│   ├── extractAudio(videoPath): String\par
│   ├── getVideoInfo(): Dict\par
│   ├── extractAudioSegment(): String\par
│   └── checkFFmpeg(): boolean\par
\par
├── OnlineDownloader (在线下载器)\par
│   ├── supportedPlatforms: String[]\par
│   ├── tempDir: String\par
│   ├── download(url): String\par
│   ├── getVideoInfo(): Dict\par
│   ├── detectPlatform(): String\par
│   └── cleanup(): void\par
\par
├── RecognitionEngine (语音识别引擎)\par
│   ├── WhisperEngine (Whisper引擎)\par
│   │   ├── modelSize: String\par
│   │   ├── whisperModel: Model\par
│   │   ├── transcribe(audioPath): String\par
│   │   ├── isAvailable(): boolean\par
│   │   └── convertLanguageCode(): String\par
│   │\par
│   ├── GoogleEngine (Google引擎)\par
│   │   ├── recognizer: Recognizer\par
│   │   ├── credentialsPath: String\par
│   │   ├── transcribe(audioPath): String\par
│   │   └── isAvailable(): boolean\par
│   │\par
│   └── AzureEngine (Azure引擎)\par
│       ├── speechKey: String\par
│       ├── speechRegion: String\par
│       ├── config: SpeechConfig\par
│       ├── transcribe(audioPath): String\par
│       └── isAvailable(): boolean\par
\par
├── RealtimeRecorder (实时录音器)\par
│   ├── sampleRate: int\par
│   ├── channels: int\par
│   ├── chunkDuration: int\par
│   ├── isRecording: boolean\par
│   ├── startRecording(): String\par
│   ├── stopRecording(): void\par
│   ├── testMicrophone(): boolean\par
│   └── getAudioDevices(): List\par
\par
├── TextFormatter (文本格式化器)\par
│   ├── punctuationMap: Dict\par
│   ├── fillerWords: Set\par
│   ├── meetingKeywords: Set\par
│   ├── format(text, options): String\par
│   ├── addPunctuation(): String\par
│   ├── detectSpeakers(): String\par
│   ├── generateSummary(): String\par
│   └── exportToFormats(): Dict\par
\par
└── UserInterface (用户界面)\par
    ├── CLIInterface (命令行界面)\par
    │   ├── console: Console\par
    │   ├── showMainMenu(): String\par
    │   ├── processAudioFile(): void\par
    │   ├── startRealtimeRecording(): void\par
    │   └── showSettings(): void\par
    │\par
    └── WebInterface (Web界面)\par
        ├── app: StreamlitApp\par
        ├── renderHeader(): void\par
        ├── renderSidebar(): Dict\par
        ├── renderFileUpload(): void\par
        └── displayResults(): void\par
\f1\fs20\par

\b\fs22 五、软件处理流程图\b0\fs20\par
\par
\b 主要业务流程：\b0\par
\par
\f0\fs18
开始\par
  ↓\par
选择输入类型\par
  ├─ 音频文件 → 音频处理模块\par
  │              ├─ 格式检查\par
  │              ├─ 音频预处理\par
  │              └─ 降噪处理\par
  │                  ↓\par
  ├─ 视频文件 → 视频处理模块\par
  │              ├─ 格式检查\par
  │              ├─ 提取音频\par
  │              └─ 音频转换\par
  │                  ↓\par
  ├─ 在线链接 → 在线下载模块\par
  │              ├─ 平台识别\par
  │              ├─ 内容下载\par
  │              └─ 音频提取\par
  │                  ↓\par
  └─ 实时录音 → 实时录音模块\par
                 ├─ 设备检测\par
                 ├─ 实时录制\par
                 └─ 流式处理\par
                     ↓\par
                语音识别引擎\par
                 ├─ Whisper → 本地识别\par
                 ├─ Google  → 云端识别\par
                 └─ Azure   → 企业识别\par
                     ↓\par
                文本后处理\par
                 ├─ 标点符号添加\par
                 ├─ 说话人识别\par
                 ├─ 段落划分\par
                 └─ 格式优化\par
                     ↓\par
                输出模块\par
                 ├─ TXT格式\par
                 ├─ Markdown格式\par
                 └─ HTML格式\par
                     ↓\par
                  完成\par
\f1\fs20\par

\b\fs22 六、技术环境信息\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx3000\cellx8000
\b 开发的硬件环境\b0\cell Intel i5以上CPU，8GB内存，100GB硬盘\cell\row
\b 运行的硬件环境\b0\cell Intel i3以上CPU，4GB内存，50GB硬盘\cell\row
\b 开发该软件的操作系统\b0\cell Windows 10/11, macOS 10.15+, Ubuntu 18.04+\cell\row
\b 软件开发环境/开发工具\b0\cell Python 3.8+, VS Code, Git\cell\row
\b 该软件的运行平台/操作系统\b0\cell Windows, macOS, Linux\cell\row
\b 软件运行支撑环境/支持软件\b0\cell Python运行环境, FFmpeg, 浏览器\cell\row
\b 编程语言\b0\cell Python, HTML, CSS, JavaScript\cell\row
\b 源程序量\b0\cell 约8000行\cell\row
\par

\b\fs22 七、功能描述\b0\fs20\par
\par
\trowd\trgaph108\trleft-108
\cellx3000\cellx8000
\b 开发目的\b0\cell 为会议记录和音视频转录提供智能化解决方案\cell\row
\b 面向领域/行业\b0\cell 办公自动化、教育培训、媒体制作、企业服务\cell\row
\b 软件的主要功能\b0\cell 支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。\cell\row
\b 软件的技术特点\b0\cell 集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。\cell\row
\par

\b\fs22 八、核心功能模块详述\b0\fs20\par
\par
\b 1. 音频处理模块\b0\par
• 支持MP3、WAV、M4A、FLAC、OGG、AAC等多种音频格式\par
• 音频预处理：格式转换、采样率调整、声道转换\par
• 音频增强：音量标准化、动态范围压缩\par
• 降噪处理：基于谱减法的噪声抑制\par
\par
\b 2. 语音识别引擎\b0\par
• OpenAI Whisper本地识别：支持多种模型大小，离线工作\par
• Google Speech-to-Text云端识别：高精度在线识别\par
• Azure认知服务：企业级可靠性\par
• 多引擎备选机制：主引擎失败时自动切换\par
\par
\b 3. 实时录音模块\b0\par
• 实时音频录制：支持麦克风和系统音频\par
• 流式转录：边录制边转录，实时显示结果\par
• 音频设备管理：自动检测和选择输入设备\par
• 音频质量监控：实时监控信号强度\par
\par
\b 4. 文本后处理模块\b0\par
• 智能标点符号添加：根据语义自动添加标点\par
• 说话人识别和标记：简单的说话人分离\par
• 文本格式化：段落划分、语句整理\par
• 会议摘要生成：提取关键信息和决策要点\par
\par

\b 注意事项：\b0\par
1. 请根据实际情况填写著作权人信息中的空白部分\par
2. 开发完成时间和发表时间请根据实际情况调整\par
3. 首次发表地点请填写具体的省市信息\par
4. 如需修改任何技术信息，请确保与实际软件功能相符\par
}