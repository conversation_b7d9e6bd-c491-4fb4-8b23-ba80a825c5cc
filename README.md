# 🎙️ 会议记录转文字稿工具

一个功能强大的会议记录转文字稿工具，支持多种输入格式和语音识别引擎。

## ✨ 功能特性

- 🎵 **多格式音频支持**: MP3, WAV, M4A, FLAC, OGG, AAC等
- 🎬 **视频文件处理**: MP4, AVI, MOV, MKV等（自动提取音频）
- 🔗 **在线链接支持**: YouTube, Bilibili, TikTok等平台链接
- 🎙️ **实时录音转录**: 支持会议实时记录
- 🤖 **多引擎识别**: OpenAI Whisper, Google Speech-to-Text, Azure等
- 📝 **智能后处理**: 自动标点、说话人识别、格式优化
- 🖥️ **多种界面**: 命令行界面和Web界面
- 📄 **多格式导出**: TXT, Markdown, HTML格式

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装FFmpeg（用于视频处理）
# Windows: 下载并安装 https://ffmpeg.org/download.html
# macOS: brew install ffmpeg
# Ubuntu: sudo apt install ffmpeg
```

### 2. 基本使用

#### 命令行界面

```bash
# 处理音频文件
python main.py --input audio.mp3 --output transcript.txt

# 处理视频文件
python main.py --input video.mp4 --engine whisper --language zh-CN

# 处理在线链接
python main.py --input "https://www.youtube.com/watch?v=..." --format-output

# 实时录音转录
python main.py --realtime --speaker-detection

# 查看帮助
python main.py --help
```

#### Web界面

```bash
# 启动Web界面
streamlit run src/ui/web.py
```

#### 交互式命令行

```bash
# 启动交互式界面
python -c "from src.ui.cli import TranscriberCLI; TranscriberCLI().run()"
```

### 3. 编程接口

```python
from src.audio.processor import AudioProcessor
from src.recognition.engine import RecognitionEngine
from src.postprocess.formatter import TextFormatter

# 处理音频
audio_processor = AudioProcessor()
processed_audio = audio_processor.preprocess("input.mp3")

# 语音识别
recognition_engine = RecognitionEngine(engine='whisper', language='zh-CN')
transcript = recognition_engine.transcribe(processed_audio)

# 文本格式化
formatter = TextFormatter()
formatted_text = formatter.format(transcript, {
    'add_punctuation': True,
    'add_paragraphs': True,
    'speaker_detection': True
})

# 保存结果
with open("transcript.txt", "w", encoding="utf-8") as f:
    f.write(formatted_text)
```

## 📁 项目结构

```
meeting-transcriber/
├── src/
│   ├── audio/          # 音频处理模块
│   ├── video/          # 视频处理模块
│   ├── online/         # 在线链接处理
│   ├── recognition/    # 语音识别引擎
│   ├── realtime/       # 实时录音功能
│   ├── postprocess/    # 文字稿后处理
│   └── ui/             # 用户界面
├── tests/              # 测试文件
├── examples/           # 示例文件
├── requirements.txt    # Python依赖
├── main.py            # 主程序入口
├── run_tests.py       # 测试运行脚本
└── README.md          # 项目说明
```

## 🔧 配置

### 环境变量配置

复制 `.env.example` 为 `.env` 并配置API密钥：

```bash
# OpenAI API (可选)
OPENAI_API_KEY=your_openai_api_key

# Google Cloud Speech-to-Text
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json

# Azure Cognitive Services Speech
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_region

# AWS Transcribe
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

## 📊 支持的格式

### 音频格式
- MP3, WAV, M4A, FLAC, OGG, AAC, WMA, OPUS, WEBM

### 视频格式
- MP4, AVI, MOV, MKV, FLV, WMV, WEBM, M4V, 3GP, OGV

### 在线平台
- YouTube, Bilibili, TikTok, 抖音, 微博, 小红书等

## 🧪 测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python run_tests.py test_audio_processor

# 使用pytest（如果安装）
pytest tests/
```

## 📝 使用示例

查看 `examples/example_usage.py` 了解详细的使用示例。

## 📄 许可证

MIT License
