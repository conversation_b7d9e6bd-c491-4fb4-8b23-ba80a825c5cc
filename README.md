# 会议记录转文字稿程序

一个功能强大的会议记录转文字稿工具，支持多种输入格式和语音识别引擎。

## 功能特性

- 🎵 **多格式音频支持**: MP3, WAV, M4A, FLAC, OGG等
- 🎬 **视频文件处理**: MP4, AVI, MOV, MKV等（自动提取音频）
- 🔗 **在线链接支持**: YouTube, 腾讯会议等平台链接
- 🎙️ **实时录音转录**: 支持会议实时记录
- 🤖 **多引擎识别**: OpenAI Whisper, Google Speech-to-Text, Azure等
- 📝 **智能后处理**: 自动标点、说话人识别、格式优化

## 项目结构

```
meeting-transcriber/
├── src/
│   ├── audio/          # 音频处理模块
│   ├── video/          # 视频处理模块
│   ├── online/         # 在线链接处理
│   ├── recognition/    # 语音识别引擎
│   ├── realtime/       # 实时录音功能
│   ├── postprocess/    # 文字稿后处理
│   └── ui/             # 用户界面
├── tests/              # 测试文件
├── examples/           # 示例文件
├── requirements.txt    # Python依赖
└── main.py            # 主程序入口
```

## 快速开始

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 运行程序
```bash
python main.py --input your_audio_file.mp3
```

## 支持的输入格式

### 音频文件
- MP3, WAV, M4A, FLAC, OGG, AAC, WMA

### 视频文件  
- MP4, AVI, MOV, MKV, FLV, WMV, WEBM

### 在线链接
- YouTube视频链接
- 腾讯会议录制链接
- 其他支持的视频平台

### 实时录音
- 麦克风实时录音
- 系统音频录制

## 许可证

MIT License
