#!/usr/bin/env python3
"""
使用示例
演示如何使用会议记录转文字稿工具
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.audio.processor import AudioProcessor
from src.video.processor import VideoProcessor
from src.online.downloader import OnlineDownloader
from src.recognition.engine import RecognitionEngine
from src.postprocess.formatter import TextFormatter


def example_audio_processing():
    """音频处理示例"""
    print("🎵 音频处理示例")
    print("-" * 30)
    
    # 创建音频处理器
    processor = AudioProcessor()
    
    # 检查支持的格式
    print("支持的音频格式:")
    formats = ['.mp3', '.wav', '.m4a', '.flac', '.ogg']
    for fmt in formats:
        supported = processor.is_supported_format(f"test{fmt}")
        print(f"  {fmt}: {'✅' if supported else '❌'}")
    
    # 示例：处理音频文件（需要实际的音频文件）
    # audio_file = "path/to/your/audio.mp3"
    # if os.path.exists(audio_file):
    #     processed_file = processor.preprocess(audio_file)
    #     print(f"处理后的文件: {processed_file}")


def example_video_processing():
    """视频处理示例"""
    print("\n🎬 视频处理示例")
    print("-" * 30)
    
    # 创建视频处理器
    processor = VideoProcessor()
    
    # 检查FFmpeg可用性
    ffmpeg_available = processor.check_ffmpeg_available()
    print(f"FFmpeg 可用性: {'✅' if ffmpeg_available else '❌'}")
    
    # 检查支持的格式
    print("支持的视频格式:")
    formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
    for fmt in formats:
        supported = processor.is_supported_format(f"test{fmt}")
        print(f"  {fmt}: {'✅' if supported else '❌'}")


def example_online_download():
    """在线下载示例"""
    print("\n🔗 在线下载示例")
    print("-" * 30)
    
    # 创建下载器
    downloader = OnlineDownloader()
    
    # 获取支持的网站
    supported_sites = downloader.get_supported_sites()
    print(f"支持的网站数量: {len(supported_sites)}")
    print("部分支持的平台:")
    common_sites = ['youtube', 'bilibili', 'tiktok']
    for site in common_sites:
        if any(site in s.lower() for s in supported_sites):
            print(f"  ✅ {site.title()}")
        else:
            print(f"  ❌ {site.title()}")


def example_recognition_engines():
    """语音识别引擎示例"""
    print("\n🤖 语音识别引擎示例")
    print("-" * 30)
    
    # 创建识别引擎
    engine = RecognitionEngine()
    
    # 获取可用引擎
    available_engines = engine.get_available_engines()
    print("可用的识别引擎:")
    for eng in available_engines:
        print(f"  ✅ {eng.title()}")
    
    # 获取引擎信息
    info = engine.get_engine_info()
    print(f"\n当前引擎: {info['current_engine']}")
    print(f"语言设置: {info['language']}")


def example_text_formatting():
    """文本格式化示例"""
    print("\n📝 文本格式化示例")
    print("-" * 30)
    
    # 创建格式化器
    formatter = TextFormatter()
    
    # 示例文本
    sample_text = "嗯 这个 就是说 我觉得这个方案很好 大家都同意吗 那么我们就这样决定了"
    
    print("原始文本:")
    print(f"  {sample_text}")
    
    # 格式化选项
    options = {
        'add_punctuation': True,
        'remove_fillers': True,
        'add_paragraphs': True,
        'speaker_detection': False
    }
    
    # 格式化文本
    formatted_text = formatter.format(sample_text, options)
    print("\n格式化后:")
    print(f"  {formatted_text}")
    
    # 导出为不同格式
    formats = formatter.export_to_formats(formatted_text, ['txt', 'md'])
    print(f"\n可导出格式: {list(formats.keys())}")


def example_complete_workflow():
    """完整工作流程示例"""
    print("\n🔄 完整工作流程示例")
    print("-" * 30)
    
    print("1. 音频预处理")
    print("2. 语音识别")
    print("3. 文本后处理")
    print("4. 格式化输出")
    print("5. 导出文件")
    
    print("\n示例代码:")
    code = '''
# 1. 处理音频
audio_processor = AudioProcessor()
processed_audio = audio_processor.preprocess("input.mp3")

# 2. 语音识别
recognition_engine = RecognitionEngine(engine='whisper', language='zh-CN')
transcript = recognition_engine.transcribe(processed_audio)

# 3. 文本格式化
formatter = TextFormatter()
options = {'add_punctuation': True, 'add_paragraphs': True}
formatted_text = formatter.format(transcript, options)

# 4. 保存结果
with open("transcript.txt", "w", encoding="utf-8") as f:
    f.write(formatted_text)
'''
    print(code)


def main():
    """主函数"""
    print("🎙️ 会议记录转文字稿工具 - 使用示例")
    print("=" * 50)
    
    # 运行各个示例
    example_audio_processing()
    example_video_processing()
    example_online_download()
    example_recognition_engines()
    example_text_formatting()
    example_complete_workflow()
    
    print("\n" + "=" * 50)
    print("📚 更多使用方法:")
    print("  命令行: python main.py --help")
    print("  Web界面: streamlit run src/ui/web.py")
    print("  交互式: python -c 'from src.ui.cli import TranscriberCLI; TranscriberCLI().run()'")


if __name__ == "__main__":
    main()
