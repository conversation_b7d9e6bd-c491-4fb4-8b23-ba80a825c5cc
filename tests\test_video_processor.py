"""
视频处理器测试
"""

import unittest
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src.video.processor import VideoProcessor


class TestVideoProcessor(unittest.TestCase):
    """视频处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = VideoProcessor()
    
    def test_supported_formats(self):
        """测试支持的格式检查"""
        # 支持的格式
        self.assertTrue(self.processor.is_supported_format("test.mp4"))
        self.assertTrue(self.processor.is_supported_format("test.avi"))
        self.assertTrue(self.processor.is_supported_format("test.mov"))
        self.assertTrue(self.processor.is_supported_format("test.mkv"))
        
        # 不支持的格式
        self.assertFalse(self.processor.is_supported_format("test.txt"))
        self.assertFalse(self.processor.is_supported_format("test.mp3"))
    
    def test_extract_audio_method_exists(self):
        """测试音频提取方法存在"""
        self.assertTrue(hasattr(self.processor, 'extract_audio'))
        self.assertTrue(callable(self.processor.extract_audio))
    
    def test_get_video_info_method_exists(self):
        """测试获取视频信息方法存在"""
        self.assertTrue(hasattr(self.processor, 'get_video_info'))
        self.assertTrue(callable(self.processor.get_video_info))
    
    def test_ffmpeg_check_method_exists(self):
        """测试FFmpeg检查方法存在"""
        self.assertTrue(hasattr(self.processor, 'check_ffmpeg_available'))
        self.assertTrue(callable(self.processor.check_ffmpeg_available))
    
    def test_extract_audio_segment_method_exists(self):
        """测试音频段提取方法存在"""
        self.assertTrue(hasattr(self.processor, 'extract_audio_segment'))
        self.assertTrue(callable(self.processor.extract_audio_segment))


if __name__ == '__main__':
    unittest.main()
