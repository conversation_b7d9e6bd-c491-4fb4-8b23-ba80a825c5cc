# 软件著作权登记信息表

## 一、填表说明：

### 1.著作权人信息：
- **名称**：公司申请填写公司全称，个人申请填写个人名字
- **地址**：公司申请填写公司营业执照上的注册地址，个人申请填写个人身份证上地址
- **证件号码**：公司/事业单位申请填写统一信用代码，个人申请填写个人身份证号码
- 若几个著作权人同时申请，请按上面要求分别填写著作权人信息

### 2.软件信息：
- 软件的全称应该以"软件、系统、平台"三词之一结尾，简称应当与全称相关，比全称短
- 软件开发完成日期一般指软件实际开发完成的日期，首次发表日期应该是软件开发完成当天或者之后
- 软件其他信息应该如实填写，并与软件的说明书和源代码相匹配

---

## 二、著作权人基础信息

| 项目 | 内容 |
|------|------|
| **名称** | [请填写著作权人姓名/公司名称] |
| **地址** | [请填写实名认证时所填写的省市地址] |
| **成立时间** | [请填写成立时间] |
| **统一信用代码** | [公司填写统一信用代码，个人填写身份证号码] |

---

## 三、软件信息

| 项目 | 内容 |
|------|------|
| **权利取得方式** | ☑ 原始取得 ☐ 继受取得 |
| **全称** | 智能会议记录转文字稿软件 |
| **简称** | 会议转录软件 |
| **版本号** | V1.0 |
| **权利范围** | ☑ 全部 ☐ 部分权利 |
| **软件分类** | ☑ 应用软件 ☐ 嵌入式软件 ☐ 中间件 ☐ 操作系统 |
| **软件说明** | ☑ 原创 ☐ 修改（升级版本） |

### 开发信息

| 项目 | 内容 |
|------|------|
| **开发方式** | ☑ 独立开发 ☐ 合作开发 ☐ 委托开发 ☐ 下达任务开发 |
| **开发完成时间** | 2025年07月29日 |
| **发表状态** | ☑ 已发表 ☐ 未发表 |
| **首次发表时间** | 2025年07月29日 |
| **首次发表地点** | [请填写具体省市] |

### 技术环境

| 项目 | 内容 |
|------|------|
| **开发的硬件环境** | Intel i5以上CPU，8GB内存，100GB硬盘 |
| **运行的硬件环境** | Intel i3以上CPU，4GB内存，50GB硬盘 |
| **开发该软件的操作系统** | Windows 10/11, macOS 10.15+, Ubuntu 18.04+ |
| **软件开发环境/开发工具** | Python 3.8+, VS Code, Git |
| **该软件的运行平台/操作系统** | Windows, macOS, Linux |
| **软件运行支撑环境/支持软件** | Python运行环境, FFmpeg, 浏览器 |
| **编程语言** | Python, HTML, CSS, JavaScript |
| **源程序量** | 约8000行 |

### 功能描述

| 项目 | 内容 |
|------|------|
| **开发目的** | 为会议记录和音视频转录提供智能化解决方案 |
| **面向领域/行业** | 办公自动化、教育培训、媒体制作、企业服务 |
| **软件的主要功能** | 支持多格式音视频文件转录，在线链接内容下载转录，实时录音转录，智能文本后处理，多种识别引擎集成，支持中英日韩等多语言，提供命令行和Web界面，导出多种格式文档。 |
| **软件的技术特点** | 集成多种语音识别引擎，支持实时音频处理，智能文本格式化，跨平台兼容性强，模块化架构设计。 |

---

## 四、软件功能详细说明

### 核心功能模块：

1. **音频处理模块**
   - 支持MP3、WAV、M4A、FLAC、OGG等多种音频格式
   - 音频预处理、降噪、格式转换功能
   - 长音频自动分割处理

2. **视频处理模块**
   - 支持MP4、AVI、MOV、MKV等主流视频格式
   - 自动提取音频轨道
   - 视频信息解析和处理

3. **在线内容处理模块**
   - 支持YouTube、Bilibili、TikTok等平台
   - 在线视频/音频下载功能
   - 多平台兼容性

4. **语音识别引擎**
   - 集成OpenAI Whisper本地识别
   - 支持Google Speech-to-Text云端识别
   - 支持Azure认知服务语音识别
   - 多引擎备选机制

5. **实时录音模块**
   - 实时音频录制和转录
   - 多设备音频输入支持
   - 实时文本输出

6. **文本后处理模块**
   - 智能标点符号添加
   - 说话人识别和标记
   - 文本格式化和段落划分
   - 会议摘要生成

7. **用户界面模块**
   - 命令行界面(CLI)
   - Web浏览器界面
   - 交互式操作界面

### 技术特色：

- **多引擎集成**：提供多种语音识别引擎选择，确保识别准确率
- **实时处理**：支持实时音频录制和转录，适用于会议场景
- **跨平台支持**：兼容Windows、macOS、Linux操作系统
- **多格式支持**：支持几乎所有主流音视频格式
- **智能后处理**：自动优化转录文本，提高可读性
- **模块化设计**：采用模块化架构，便于维护和扩展

---

**注意事项**：
1. 请根据实际情况填写著作权人信息
2. 开发完成时间和发表时间请根据实际情况调整
3. 硬件环境要求可根据实际测试结果调整
4. 源程序量请根据实际代码统计结果填写
