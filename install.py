#!/usr/bin/env python3
"""
安装脚本
自动安装依赖和配置环境
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def check_ffmpeg():
    """检查FFmpeg"""
    print("🎬 检查FFmpeg...")
    
    try:
        subprocess.run(["ffmpeg", "-version"], 
                      capture_output=True, check=True)
        print("✅ FFmpeg 已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ FFmpeg 未安装")
        print("请手动安装FFmpeg:")
        print("  Windows: https://ffmpeg.org/download.html")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu: sudo apt install ffmpeg")
        return False

def setup_environment():
    """设置环境"""
    print("⚙️ 设置环境...")
    
    # 复制环境变量示例文件
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        shutil.copy(env_example, env_file)
        print("✅ 已创建 .env 文件")
        print("请编辑 .env 文件配置API密钥")
    
    return True

def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "run_tests.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
            return True
        else:
            print("⚠️ 部分测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"⚠️ 测试运行失败: {e}")
        return False

def show_usage_info():
    """显示使用信息"""
    print("\n" + "="*50)
    print("🎉 安装完成！")
    print("="*50)
    print("\n📚 使用方法:")
    print("  命令行界面:")
    print("    python main.py --help")
    print("    python main.py --input audio.mp3")
    print("    python main.py --realtime")
    print()
    print("  Web界面:")
    print("    python start_web.py")
    print("    或: streamlit run src/ui/web.py")
    print()
    print("  交互式界面:")
    print("    python -c \"from src.ui.cli import TranscriberCLI; TranscriberCLI().run()\"")
    print()
    print("  示例代码:")
    print("    python examples/example_usage.py")
    print()
    print("📖 更多信息请查看 README.md")

def main():
    """主安装流程"""
    print("🎙️ 会议记录转文字稿工具 - 安装程序")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_requirements():
        print("❌ 安装失败")
        sys.exit(1)
    
    # 检查FFmpeg
    check_ffmpeg()
    
    # 设置环境
    setup_environment()
    
    # 运行测试
    run_tests()
    
    # 显示使用信息
    show_usage_info()

if __name__ == "__main__":
    main()
